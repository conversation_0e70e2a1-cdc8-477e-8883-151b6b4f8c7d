<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="chrome=1">
        <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
        <link rel="stylesheet" href="../resources/css/base.css" />
        <link rel="stylesheet" href="../common/css/style.css" />
    </head>
    <body>
        <div class="instructions">Resize the page to see the Tether flip.</div>

        <div class="element"></div>
        <div class="target"></div>

        <script src="//github.hubspot.com/tether/dist/js/tether.js"></script>
        <script>
            new Tether({
                element: '.element',
                target: '.target',
                attachment: 'top left',
                targetAttachment: 'top right',
                constraints: [{
                    to: 'window',
                    attachment: 'together'
                }]
            });
        </script>
    </body>
</html>
