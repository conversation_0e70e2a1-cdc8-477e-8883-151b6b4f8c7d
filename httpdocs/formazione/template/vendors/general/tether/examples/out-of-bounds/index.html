<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="chrome=1">
        <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
        <link rel="stylesheet" href="../resources/css/base.css" />
        <link rel="stylesheet" href="../common/css/style.css" />
        <style>
            .tether-element.tether-out-of-bounds {
                display: none;
            }
        </style>
    </head>
    <body>
        <div class="instructions">Resize the screen to see the tethered element disappear when it can't fit.</div>

        <div class="element"></div>
        <div class="target"></div>

        <script src="//github.hubspot.com/tether/dist/js/tether.js"></script>
        <script>
            var tether = new Tether({
                element: '.element',
                target: '.target',
                attachment: 'top left',
                targetAttachment: 'top right',
                constraints: [{
                    to: 'window',
                    attachment: 'together'
                }]
            });
            tether.on('update', function(event) {
                console.log(event);
            });
        </script>
    </body>
</html>
