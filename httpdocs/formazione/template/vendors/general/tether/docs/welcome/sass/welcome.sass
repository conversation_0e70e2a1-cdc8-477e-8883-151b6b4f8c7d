@import inline-block

html, body
    height: 100%

body
    margin: 0
    font-family: "proxima-nova", "Helvetica Neue", sans-serif

.button
    display: inline-block
    border: 2px solid #333
    color: #333
    padding: 1em 1.25em
    font-weight: 500
    text-transform: uppercase
    letter-spacing: 3px
    text-decoration: none
    cursor: pointer
    width: 140px
    font-size: .8em
    line-height: 1.3em
    text-align: center

.tether-element.tether-theme-arrows-dark .tether-content
    padding: 1em
    font-size: 1.1em

    .button
        border-color: #fff
        color: #fff
        width: 170px
        pointer-events: all

.mobile-copy
    display: none

    @media (max-width: 568px)
        display: block

.button.dark
    background: #333
    color: #fff

.hero-wrap
    height: 100%
    overflow: hidden

table.showcase
    height: 100%
    width: 100%
    position: relative

    &:after
        content: ""
        display: block
        position: absolute
        left: 0
        right: 0
        bottom: 20px
        margin: auto
        height: 0
        width: 0
        border-width: 18px
        border-style: solid
        border-color: transparent
        border-top-color: rgba(0, 0, 0, 0.2)

    &.no-next-arrow:after
        display: none

    .showcase-inner
        margin: 40px auto 60px
        padding: 10px

        h1
            font-size: 50px
            text-align: center
            font-weight: 300

            @media (max-width: 567px)
                font-size: 40px

        h2
            font-size: 24px
            text-align: center
            font-weight: 300
            margin: 1em 0 1em

            @media (max-width: 567px)
                font-size: 14px

        p
            text-align: center

    &.hero
        text-align: center

        .tether-target-demo
            +inline-block
            border: 2px dotted #000
            margin: 5rem auto
            padding: 5rem

            @media (max-width: 567px)
                padding: 1rem

    &.share
        background: #f3f3f3

    &.projects-showcase .showcase-inner

        .projects-list
            width: 80%
            max-width: 1200px
            margin: 0 auto

            .project
                color: inherit
                text-decoration: none
                position: relative
                width: 50%
                float: left
                text-align: center
                margin-bottom: 2rem

                &:nth-child(odd)
                    clear: left

            .os-icon
                width: 8rem
                height: 8rem
                margin-bottom: 1rem
                background-size: 100%

            h1
                font-size: 2.5rem

            p
                font-size: 1.3rem

    &.browser-demo
        background-image: linear-gradient(top left, #723362 0%, #9d223c 100%)
        background-color: #9d223c
        position: absolute
        top: 100%

        &.fixed
            position: fixed
            top: 0
            bottom: 0
            left: 0
            right: 0
            z-index: 1

            .browser-demo-inner
                transition: width 2s ease-in-out, height 2s ease-in-out

            // Sections

            &[data-section="what"]
                box-shadow: 0 0 0 0

            &[data-section="why"]

                .browser-demo-inner
                    width: 70%

            &[data-section="outro"]

                .showcase-inner
                    pointer-events: all

        .showcase-inner
            pointer-events: none
            position: absolute
            left: 10%
            right: 40%
            top: 220px
            bottom: 120px
            margin: 0
            padding: 0

            @media (max-width: 567px)
                bottom: 90px
                top: 180px

        .browser-demo-inner
            height: 100%
            width: 100%

        .section-copy
            transition: opacity .5s ease-in-out, top .5s ease-in-out
            opacity: 0
            position: absolute
            top: 0
            position: absolute
            height: 200px
            color: #fff
            text-align: center
            width: 100%

            &.active
                opacity: 1
                top: -150px

                @media (max-width: 567px)
                    top: -130px

            h2
                font-size: 40px
                font-weight: bold
                line-height: 1
                margin: 25px 0 15px

                @media (max-width: 567px)
                    font-size: 30px

        .browser-window
            border-radius: 4px
            background: #fff
            position: relative
            height: 100%
            width: 100%
            max-width: 1200px
            margin: 0 auto

            .browser-titlebar
                position: absolute
                top: 0
                left: 0
                right: 0
                border-bottom: 1px solid #eee
                height: 55px

                .browser-dots
                    padding: 16px

                    b
                        +inline-block
                        border-radius: 50%
                        width: 10px
                        height: 10px
                        margin-right: 7px
                        background: rgba(0, 0, 0, .1)

            .browser-frame
                position: absolute
                top: 55px
                left: 0
                right: 0
                bottom: 0

                iframe
                    border-radius: 0 0 4px 4px
                    border: 0
                    width: 100%
                    height: 100%

    &.browser-demo-section

        .section-scroll-copy
            position: relative
            z-index: 10
            color: #fff
            width: 100%
            font-size: 22px

            .section-scroll-copy-inner
                position: absolute
                z-index: 10
                color: #fff
                right: 10%
                width: 23%

                a
                    color: inherit

                .example-paragraph
                    border-radius: 4px
                    background: #000
                    padding: 1rem

.browser-content
    display: none
