@import inline-block

html, body
    height: 100%
    overflow: hidden
    font-family: "proxima-nova", sans-serif

.tether.tether-theme-arrows-dark .tether-content
    filter: none
    background: #000

    ul
        color: #fff
        list-style: none
        padding: 0
        margin: 0

.tether.tether-theme-arrows-dark.tether-element-attached-top.tether-element-attached-left.tether-target-attached-right .tether-content:before
    border-right-color: #000

.browser-demo
    position: absolute
    top: 0
    left: 0
    bottom: 0
    right: 0

    *, *:after, *:before
        box-sizing: border-box

    .top
        position: absolute
        height: 60px
        padding: 20px
        line-height: 40px
        width: 100%
        border-bottom: 1px solid rgba(0, 0, 0, .1)

    .bottom
        position: absolute
        top: 60px
        bottom: 0
        width: 100%

        .left
            border-right: 1px solid rgba(0, 0, 0, .1)
            position: absolute
            width: 30%
            height: 100%
            overflow: auto

            .item
                height: 64px
                border-bottom: 1px solid rgba(0, 0, 0, .1)
                cursor: pointer

                &:hover, &.tether-open
                    background: rgba(0, 0, 0, .1)
                    border-bottom: 1px solid rgba(0, 0, 0, 0)

                &:last-child
                    border-bottom: 0

        .right
            position: absolute
            width: 70%
            right: 0
            height: 100%
            padding: 20px

    .title
        +inline-block
        background: rgba(0, 0, 0, .1)
        width: 150px
        height: 15px
        margin-bottom: 20px

    .word
        +inline-block
        background: rgba(0, 0, 0, .1)
        width: 50px
        height: 8px
        margin-right: 5px
        margin-bottom: 5px

        &:nth-last-child(4n+1)
            width: 73px

        &:nth-last-child(10n+1)
            width: 14px

        &:nth-last-child(9n+1)
            width: 80px
