{"name": "bootstrap", "description": "The most popular front-end framework for developing responsive, mobile first projects on the web.", "version": "4.3.1", "version_short": "4.3", "keywords": ["css", "sass", "mobile-first", "responsive", "front-end", "framework", "web"], "homepage": "https://getbootstrap.com/", "author": "The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)", "contributors": ["Twitter, Inc."], "scripts": {"start": "npm-run-all --parallel watch docs-serve", "blc": "blc --exclude-external --filter-level 3 --follow --get --ordered --recursive --host-requests 4 --input http://localhost:3000/", "http-server": "http-server --silent -p 3000", "bundlesize": "bundlesize", "check-broken-links": "npm-run-all --parallel --race \"http-server -- _gh_pages/\" blc", "css": "npm-run-all css-compile css-prefix css-minify css-copy", "css-copy": "cross-env-shell shx mkdir -p site/docs/$npm_package_version_short/dist/ && cross-env-shell shx cp -r dist/css/ site/docs/$npm_package_version_short/dist/", "css-main": "npm-run-all css-lint css-compile-main css-prefix-main css-minify-main css-copy", "css-docs": "npm-run-all css-compile-docs css-prefix-docs css-minify-docs", "css-compile": "npm-run-all --parallel css-compile-*", "css-compile-main": "node-sass --output-style expanded --source-map true --source-map-contents true --precision 6 scss/bootstrap.scss dist/css/bootstrap.css && node-sass --output-style expanded --source-map true --source-map-contents true --precision 6 scss/bootstrap-grid.scss dist/css/bootstrap-grid.css && node-sass --output-style expanded --source-map true --source-map-contents true --precision 6 scss/bootstrap-reboot.scss dist/css/bootstrap-reboot.css && npm run css-copy", "css-compile-docs": "cross-env-shell node-sass --output-style expanded --source-map true --source-map-contents true --precision 6 site/docs/$npm_package_version_short/assets/scss/docs.scss site/docs/$npm_package_version_short/assets/css/docs.min.css", "css-lint": "npm-run-all --parallel css-lint-*", "css-lint-main": "stylelint \"scss/**/*.scss\" --cache --cache-location .cache/.stylelintcache", "css-lint-docs": "stylelint \"site/docs/**/assets/scss/*.scss\" \"site/docs/**/*.css\" --cache --cache-location .cache/.stylelintcache", "css-lint-vars": "fusv scss/ site/docs/", "css-minify": "npm-run-all --parallel css-minify-*", "css-minify-main": "cleancss --level 1 --format breaksWith=lf --source-map --source-map-inline-sources --output dist/css/bootstrap.min.css dist/css/bootstrap.css && cleancss --level 1 --format breaksWith=lf --source-map --source-map-inline-sources --output dist/css/bootstrap-grid.min.css dist/css/bootstrap-grid.css && cleancss --level 1 --format breaksWith=lf --source-map --source-map-inline-sources --output dist/css/bootstrap-reboot.min.css dist/css/bootstrap-reboot.css", "css-minify-docs": "cross-env-shell cleancss --level 1 --format breaksWith=lf --source-map --source-map-inline-sources --output site/docs/$npm_package_version_short/assets/css/docs.min.css site/docs/$npm_package_version_short/assets/css/docs.min.css", "css-prefix": "npm-run-all --parallel css-prefix-*", "css-prefix-main": "postcss --config build/postcss.config.js --replace \"dist/css/*.css\" \"!dist/css/*.min.css\"", "css-prefix-docs": "postcss --config build/postcss.config.js --replace \"site/docs/**/*.css\" \"site/docs/**/*.css\"", "js": "npm-run-all js-compile js-minify js-copy", "js-copy": "cross-env-shell shx mkdir -p site/docs/$npm_package_version_short/dist/ && cross-env-shell shx cp -r dist/js/ site/docs/$npm_package_version_short/dist/", "js-main": "npm-run-all js-lint js-compile js-minify-main", "js-docs": "npm-run-all js-lint-docs js-minify-docs", "js-compile": "npm-run-all --parallel js-compile-* --sequential js-copy", "js-compile-standalone": "rollup --environment BUNDLE:false --config build/rollup.config.js --sourcemap", "js-compile-bundle": "rollup --environment BUNDLE:true --config build/rollup.config.js --sourcemap", "js-compile-plugins": "node build/build-plugins.js", "js-compile-plugins-coverage": "cross-env NODE_ENV=test node build/build-plugins.js", "js-lint": "npm-run-all --parallel js-lint-*", "js-lint-main": "eslint --cache --cache-location .cache/.eslintcache js/src js/tests build/", "js-lint-docs": "eslint --cache --cache-location .cache/.eslintcache site/", "js-minify": "npm-run-all --parallel js-minify-main js-minify-docs", "js-minify-main": "npm-run-all js-minify-standalone js-minify-bundle", "js-minify-standalone": "uglifyjs --compress typeofs=false --mangle --comments \"/^!/\" --source-map \"content=dist/js/bootstrap.js.map,includeSources,url=bootstrap.min.js.map\" --output dist/js/bootstrap.min.js dist/js/bootstrap.js", "js-minify-bundle": "uglifyjs --compress typeofs=false --mangle --comments \"/^!/\" --source-map \"content=dist/js/bootstrap.bundle.js.map,includeSources,url=bootstrap.bundle.min.js.map\" --output dist/js/bootstrap.bundle.min.js dist/js/bootstrap.bundle.js", "js-minify-docs": "cross-env-shell uglifyjs --mangle --comments \\\"/^!/\\\" --output site/docs/$npm_package_version_short/assets/js/docs.min.js site/docs/$npm_package_version_short/assets/js/vendor/anchor.min.js site/docs/$npm_package_version_short/assets/js/vendor/clipboard.min.js site/docs/$npm_package_version_short/assets/js/vendor/bs-custom-file-input.min.js \"site/docs/$npm_package_version_short/assets/js/src/*.js\"", "js-test": "npm-run-all js-test-karma* js-test-integration", "js-test-karma": "karma start js/tests/karma.conf.js", "js-test-karma-old": "cross-env USE_OLD_JQUERY=true npm run js-test-karma", "js-test-karma-bundle": "cross-env BUNDLE=true npm run js-test-karma", "js-test-karma-bundle-old": "cross-env BUNDLE=true USE_OLD_JQUERY=true npm run js-test-karma", "js-test-integration": "rollup --config js/tests/integration/rollup.bundle.js", "js-test-cloud": "cross-env BROWSER=true npm run js-test-karma", "lint": "npm-run-all --parallel js-lint css-lint", "coveralls": "shx cat js/coverage/lcov.info | coveralls", "docs": "npm-run-all css-docs js-docs docs-compile docs-lint", "docs-compile": "bundle exec jekyll build", "docs-production": "cross-env JEKYLL_ENV=production npm run docs-compile", "docs-lint": "node build/vnu-jar.js", "docs-serve": "bundle exec jekyll serve", "docs-serve-only": "npm run docs-serve -- --skip-initial-build --no-watch", "update-deps": "ncu -a -x jquery -x bundlesize && npm update && bundle update && cross-env-shell echo Manually update \\\"site/docs/$npm_package_version_short/assets/js/vendor/\\\"", "release": "npm run dist && npm run release-sri && npm run release-zip && npm run docs-production", "release-sri": "node build/generate-sri.js", "release-version": "node build/change-version.js", "release-zip": "cross-env-shell \"shx rm -rf bootstrap-$npm_package_version-dist && shx cp -r dist/ bootstrap-$npm_package_version-dist && zip -r9 bootstrap-$npm_package_version-dist.zip bootstrap-$npm_package_version-dist && shx rm -rf bootstrap-$npm_package_version-dist\"", "dist": "npm-run-all --parallel css js", "test": "npm-run-all lint dist js-test docs-compile docs-lint bundlesize", "watch": "npm-run-all --parallel watch-*", "watch-css-main": "nodemon --watch scss/ --ext scss --exec \"npm run css-main\"", "watch-css-docs": "nodemon --watch \"site/docs/**/assets/scss/\" --ext scss --exec \"npm run css-docs\"", "watch-js-main": "nodemon --watch js/src/ --ext js --exec \"npm run js-compile\"", "watch-js-docs": "nodemon --watch \"site/docs/**/assets/js/src/\" --ext js --exec \"npm run js-docs\""}, "style": "dist/css/bootstrap.css", "sass": "scss/bootstrap.scss", "main": "dist/js/bootstrap", "repository": {"type": "git", "url": "git+https://github.com/twbs/bootstrap.git"}, "bugs": {"url": "https://github.com/twbs/bootstrap/issues"}, "license": "MIT", "dependencies": {}, "peerDependencies": {"jquery": "1.9.1 - 3", "popper.js": "^1.14.7"}, "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.2.2", "@babel/plugin-proposal-object-rest-spread": "^7.3.2", "@babel/preset-env": "^7.3.1", "autoprefixer": "^9.4.7", "babel-eslint": "^10.0.1", "babel-plugin-istanbul": "^5.1.0", "broken-link-checker": "^0.7.8", "bundlesize": "0.15.3", "clean-css-cli": "^4.2.1", "coveralls": "^3.0.2", "cross-env": "^5.2.0", "eslint": "^5.13.0", "find-unused-sass-variables": "^0.3.2", "glob": "^7.1.3", "hammer-simulator": "0.0.1", "http-server": "^0.11.1", "ip": "^1.1.5", "jquery": "^3.3.1", "karma": "^3.1.4", "karma-browserstack-launcher": "^1.4.0", "karma-chrome-launcher": "^2.2.0", "karma-coverage-istanbul-reporter": "^2.0.4", "karma-detect-browsers": "^2.3.3", "karma-firefox-launcher": "^1.1.0", "karma-qunit": "^2.1.0", "karma-sinon": "^1.0.5", "node-sass": "^4.11.0", "nodemon": "^1.18.9", "npm-run-all": "^4.1.5", "popper.js": "^1.14.7", "postcss-cli": "^6.1.1", "qunit": "^2.9.1", "rollup": "^1.1.2", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-commonjs": "^9.2.0", "rollup-plugin-node-resolve": "^4.0.0", "shelljs": "^0.8.3", "shx": "^0.3.2", "sinon": "^7.2.3", "stylelint": "^9.10.1", "stylelint-config-twbs-bootstrap": "^0.3.0", "uglify-js": "^3.4.9", "vnu-jar": "18.11.5"}, "engines": {"node": ">=6"}, "files": ["dist/{css,js}/*.{css,js,map}", "js/{src,dist}/**/*.{js,map}", "scss/**/*.scss"], "bundlesize": [{"path": "./dist/css/bootstrap-grid.css", "maxSize": "7 kB"}, {"path": "./dist/css/bootstrap-grid.min.css", "maxSize": "6 kB"}, {"path": "./dist/css/bootstrap-reboot.css", "maxSize": "2 kB"}, {"path": "./dist/css/bootstrap-reboot.min.css", "maxSize": "2 kB"}, {"path": "./dist/css/bootstrap.css", "maxSize": "25 kB"}, {"path": "./dist/css/bootstrap.min.css", "maxSize": "23 kB"}, {"path": "./dist/js/bootstrap.bundle.js", "maxSize": "47 kB"}, {"path": "./dist/js/bootstrap.bundle.min.js", "maxSize": "22 kB"}, {"path": "./dist/js/bootstrap.js", "maxSize": "25 kB"}, {"path": "./dist/js/bootstrap.min.js", "maxSize": "15.5 kB"}], "jspm": {"registry": "npm", "main": "js/bootstrap", "directories": {"lib": "dist"}, "shim": {"js/bootstrap": {"deps": ["j<PERSON>y", "popper.js"], "exports": "$"}}, "dependencies": {}, "peerDependencies": {"jquery": "1.9.1 - 3", "popper.js": "^1.14.7"}}}