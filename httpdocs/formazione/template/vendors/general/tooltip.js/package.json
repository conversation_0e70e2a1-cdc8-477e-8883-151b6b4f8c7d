{"name": "tooltip.js", "version": "1.3.1", "description": "A kickass library to create tooltips, based on Popper.js", "license": "MIT", "main": "./dist/umd/tooltip.js", "module": "./dist/esm/tooltip.js", "types": "index.d.ts", "scripts": {"build": "node bundle.js", "prepublish": "yarn build", "pretest": "yarn lint", "test": "popper-karma", "posttest": "tsc --project tests/types/tsconfig.json", "test:dev": "BROWSERS=Chrome NODE_ENV=development yarn test", "lint": "eslint .", "coverage": "COVERAGE=true yarn test"}, "repository": {"type": "git", "url": "git+https://github.com/FezVrasta/popper.js.git"}, "dependencies": {"popper.js": "^1.0.2"}, "devDependencies": {"@popperjs/bundle": "^1.0.0", "@popperjs/eslint-config-popper": "^1.0.0", "@popperjs/test": "^1.0.0", "@popperjs/test-utils": "^1.0.0", "eslint": "^4.1.1", "eslint-plugin-jasmine": "^2.6.2", "typescript": "^2.7.1"}}