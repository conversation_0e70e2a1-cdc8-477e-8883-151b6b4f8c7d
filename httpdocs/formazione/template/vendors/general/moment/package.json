{"name": "moment", "version": "2.24.0", "description": "Parse, validate, manipulate, and display dates", "homepage": "http://momentjs.com", "author": "Iskren Ivov Chernev <<EMAIL>> (https://github.com/ichernev)", "contributors": ["<PERSON> <<EMAIL>> (http://timwoodcreates.com/)", "<PERSON> (http://rockymeza.com)", "<PERSON> <<EMAIL>> (http://codeofmatt.com)", "<PERSON> <<EMAIL>> (http://isaaccambron.com)", "<PERSON> <<EMAIL>> (https://github.com/oire)"], "keywords": ["moment", "date", "time", "parse", "format", "validate", "i18n", "l10n", "ender"], "main": "./moment.js", "jsnext:main": "./src/moment.js", "typings": "./moment.d.ts", "engines": {"node": "*"}, "repository": {"type": "git", "url": "https://github.com/moment/moment.git"}, "bugs": {"url": "https://github.com/moment/moment/issues"}, "license": "MIT", "devDependencies": {"benchmark": "latest", "coveralls": "^3.0.2", "es6-promise": "latest", "grunt": "~0.4", "grunt-benchmark": "latest", "grunt-cli": "latest", "grunt-contrib-clean": "latest", "grunt-contrib-concat": "latest", "grunt-contrib-copy": "latest", "grunt-contrib-jshint": "latest", "grunt-contrib-uglify": "latest", "grunt-contrib-watch": "^1.1.0", "grunt-env": "latest", "grunt-exec": "latest", "grunt-jscs": "latest", "grunt-karma": "latest", "grunt-nuget": "latest", "grunt-string-replace": "latest", "karma": "latest", "karma-chrome-launcher": "latest", "karma-firefox-launcher": "latest", "karma-qunit": "latest", "karma-sauce-launcher": "latest", "load-grunt-tasks": "~3.5.2", "node-qunit": "^1.0.0", "nyc": "~11.9.0", "qunit": "^2.7.1", "rollup": "~0.67.4", "spacejam": "latest", "typescript": "^1.8.10", "uglify-js": "latest"}, "ender": "./ender.js", "dojoBuild": "package.js", "jspm": {"files": ["moment.js", "moment.d.ts", "locale"], "map": {"moment": "./moment"}, "buildConfig": {"uglify": true}}, "scripts": {"typescript-test": "tsc --project typing-tests", "test": "grunt test", "coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls"}, "spm": {"main": "moment.js", "output": ["locale/*.js"]}}