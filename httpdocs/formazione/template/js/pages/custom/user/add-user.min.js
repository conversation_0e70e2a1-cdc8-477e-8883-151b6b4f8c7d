"use strict";var KTAppUserAdd=function(){var e,r,t;return{init:function(){var n;e=$("#kt_apps_user_add_user_form"),(t=new KTWizard("kt_apps_user_add_user",{startStep:1})).on("beforeNext",function(e){!0!==r.form()&&e.stop()}),t.on("change",function(e){KTUtil.scrollTop()}),r=e.validate({ignore:":hidden",rules:{profile_avatar:{},profile_first_name:{required:!0},profile_last_name:{required:!0},profile_phone:{required:!0},profile_email:{required:!0,email:!0}},invalidHandler:function(e,r){KTUtil.scrollTop(),swal.fire({title:"",text:"There are some errors in your submission. Please correct them.",type:"error",buttonStyling:!1,confirmButtonClass:"btn btn-brand btn-sm btn-bold"})},submitHandler:function(e){}}),(n=e.find('[data-ktwizard-type="action-submit"]')).on("click",function(t){t.preventDefault(),r.form()&&(KTApp.progress(n),e.ajaxSubmit({success:function(){KTApp.unprogress(n),swal.fire({title:"",text:"The application has been successfully submitted!",type:"success",confirmButtonClass:"btn btn-secondary"})}}))}),new KTAvatar("kt_apps_user_add_user_avatar")}}}();jQuery(document).ready(function(){KTAppUserAdd.init()});