"use strict";var KTMorrisChartsDemo={init:function(){new Morris.Line({element:"kt_morris_1",data:[{y:"2006",a:100,b:90},{y:"2007",a:75,b:65},{y:"2008",a:50,b:40},{y:"2009",a:75,b:65},{y:"2010",a:50,b:40},{y:"2011",a:75,b:65},{y:"2012",a:100,b:90}],xkey:"y",ykeys:["a","b"],labels:["Values A","Values B"],lineColors:["#6e4ff5","#f6aa33"]}),new Morris.Area({element:"kt_morris_2",data:[{y:"2006",a:100,b:90},{y:"2007",a:75,b:65},{y:"2008",a:50,b:40},{y:"2009",a:75,b:65},{y:"2010",a:50,b:40},{y:"2011",a:75,b:65},{y:"2012",a:100,b:90}],xkey:"y",ykeys:["a","b"],labels:["Series A","Series B"],lineColors:["#de1f78","#c7d2e7"],pointFillColors:["#fe3995","#e6e9f0"]}),new Morris.Bar({element:"kt_morris_3",data:[{y:"2006",a:100,b:90},{y:"2007",a:75,b:65},{y:"2008",a:50,b:40},{y:"2009",a:75,b:65},{y:"2010",a:50,b:40},{y:"2011",a:75,b:65},{y:"2012",a:100,b:90}],xkey:"y",ykeys:["a","b"],labels:["Series A","Series B"],barColors:["#2abe81","#24a5ff"]}),new Morris.Donut({element:"kt_morris_4",data:[{label:"Download Sales",value:12},{label:"In-Store Sales",value:30},{label:"Mail-Order Sales",value:20}],colors:["#593ae1","#6e4ff5","#9077fb"]})}};jQuery(document).ready(function(){KTMorrisChartsDemo.init()});