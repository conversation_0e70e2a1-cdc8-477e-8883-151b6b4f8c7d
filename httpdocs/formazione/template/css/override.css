/*
*
* Portaleagendo header styles port::START
*
 */
html {
    scroll-behavior: smooth;
}
body > header {
    background: #e6e6e6;
    border-top: 0;
    border-bottom: 2px solid #d2d2d2;
    text-align: right;
}
body > header .x-logo {
    float: left;
    margin: 1rem 2rem;
}
body > header ul {
    padding-left: 0;
    list-style: none outside none;
    margin: 0;
    margin-top: 10px;
}
body > header ul li {
    display: inline-block;
}
body > header .x-btn {
    background: #e6e6e6;
    border: 0;
    -webkit-border-radius: 0.25rem;
    -moz-border-radius: 0.25rem;
    border-radius: 0.25rem;
    margin: 1rem 0.2rem;
    padding: 0.5rem 0.7rem;
    color: #999999;
    text-decoration: none;
    cursor: pointer;
}
body > header .x-btn.x-active,
body > header .x-btn:hover {
    background: #cdcdcd;
    color: #fff;
    text-decoration: none;
}
body > header .x-logo {
    float: left;
    margin: 1rem 2rem;
}
.logo-groupama {
    float: left;
    margin: 1.8rem 0rem;
}
#x-widget-user {
    display: inline-block;
    background-color: white;
    color: rgb(0, 106, 83);
    padding: 2px 10px;
    font-size: 1rem;
    font-weight: 400;
    cursor: pointer;
    border-radius: 0.25rem;
    margin: 1rem 2rem;
}
@media (min-width: 48.1rem) {
    #x-nav-btn {
        display: none;
    }
}
/*
*
* Portaleagendo header styles port::END
*
 */


/*
*
* Template overrides::START
*
 */

@font-face {
    font-family: 'Roboto Condensed';
    font-style: italic;
    font-weight: 300;
    src: local('Roboto Condensed Light Italic'), local('RobotoCondensed-LightItalic'), url(https://fonts.gstatic.com/s/robotocondensed/v17/ieVg2ZhZI2eCN5jzbjEETS9weq8-19eDpCEobdNc.ttf) format('truetype');
}
@font-face {
    font-family: 'Roboto Condensed';
    font-style: italic;
    font-weight: 400;
    src: local('Roboto Condensed Italic'), local('RobotoCondensed-Italic'), url(https://fonts.gstatic.com/s/robotocondensed/v17/ieVj2ZhZI2eCN5jzbjEETS9weq8-19eLDwM4.ttf) format('truetype');
}
@font-face {
    font-family: 'Roboto Condensed';
    font-style: italic;
    font-weight: 700;
    src: local('Roboto Condensed Bold Italic'), local('RobotoCondensed-BoldItalic'), url(https://fonts.gstatic.com/s/robotocondensed/v17/ieVg2ZhZI2eCN5jzbjEETS9weq8-19eDtCYobdNc.ttf) format('truetype');
}
@font-face {
    font-family: 'Roboto Condensed';
    font-style: normal;
    font-weight: 300;
    src: local('Roboto Condensed Light'), local('RobotoCondensed-Light'), url(https://fonts.gstatic.com/s/robotocondensed/v17/ieVi2ZhZI2eCN5jzbjEETS9weq8-33mZGCQYag.ttf) format('truetype');
}
@font-face {
    font-family: 'Roboto Condensed';
    font-style: normal;
    font-weight: 400;
    src: local('Roboto Condensed'), local('RobotoCondensed-Regular'), url(https://fonts.gstatic.com/s/robotocondensed/v17/ieVl2ZhZI2eCN5jzbjEETS9weq8-19K7CA.ttf) format('truetype');
}
@font-face {
    font-family: 'Roboto Condensed';
    font-style: normal;
    font-weight: 700;
    src: local('Roboto Condensed Bold'), local('RobotoCondensed-Bold'), url(https://fonts.gstatic.com/s/robotocondensed/v17/ieVi2ZhZI2eCN5jzbjEETS9weq8-32meGCQYag.ttf) format('truetype');
}

* {
    font-family: 'Roboto Condensed', sans-serif;
}
html {
    font-size: 14px;
}

.kt-scrolltop--on .kt-header__brand .text-white {
    color: #006A53 !important;
}

.bgrBackend {
    background-image: url(../media/bg/HeaderBACKEND.jpg);
}

.bgrFrontend {
    background-image: url(../media/bg/HeaderFRONTEND.jpg);
}

.kt-bg-direzione {
    background: #006A53;
}
.kt-badge.kt-badge--direzione {
    background: #006A53;
    color: white;
}
.kt-font-direzione {
    color: #006A53;
}
.kt-badge.kt-badge--area {
    background: #C7D300;
    color: white;
}
.kt-bg-area {
    background: #C7D300;
}
.kt-font-area {
    color: #C7D300;
}
.kt-badge.kt-badge--learning {
    background: #4687E6;
    color: white;
}
.kt-bg-learning {
    background: #4687E6;
}
.kt-font-learning {
    color: #4687E6;
}

.kt-bg-selfcertificated {
    background: #ffb822;
}
.kt-font-selfcertificated {
    color: #ffb822;
}

.kt-bg-residual {
    background: #D4D4D4;
}

.kt-header__topbar .kt-header__topbar-item.kt-header__topbar-item--user .kt-header__topbar-welcome {
    font-size: 1.125rem;
}

.table thead {
    background-color: #ffffff;
}

#table-files th:nth-child(1) {
    text-align: left;
}

#table-files th {
    text-align: center;
}

#table-files th:nth-child(3) {
    text-align: right;
}

.dataTables_wrapper .dataTable th {
    /*color: white;*/
}

.dataTables_wrapper .dataTable {
    margin-top: 0!important;
}

.blue-spacer {
    height: 5px;
    background-color: #366CF3;
    margin: 0;
}

.nav-line-info {
    color: #366CF3;
    border-bottom: 1px solid #366CF3;
}

.ng-table th.sortable .sort-indicator:before {
    border-top: 4px solid #fff;
}

.ng-table th.sortable .sort-indicator {
    white-space: nowrap;
}

.ng-table th.sortable .sort-indicator:after, .ng-table th.sortable .sort-indicator:before {
    border-color: #000 transparent;
    opacity: 0.6;
}

.table.row-hover td:first-of-type {
    border-left: 10px solid transparent;
}

.table.row-hover tr:hover td:first-child {
    border-left: 10px solid #366CF3;
}

.table .strike {
    text-decoration: line-through;
}

.table .row-deselected {
    background-color: #f7f8fa;
}

.table.row-selectable tr {
    cursor: pointer;
}

.table.row-selectable tr:hover td.highlight-helper {
    font-weight: bold;
}

.table tbody td,
.table thead th {
    font-weight: 300;
    font-size: 1.125rem;
}

.table thead th {
    text-align: left;
}

.table-striped tbody tr:nth-of-type(even) {
    background-color: #f7f8fa;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: #ffffff;
}

.table select.form-control {
    padding: 0!important;
}

@media (min-width: 1025px) {
    .kt-form.kt-form--label-right .form-group label:not(.kt-checkbox):not(.kt-radio):not(.kt-option) {
        text-align: left;
    }
    .kt-header-menu .kt-menu__nav > .kt-menu__item > .kt-menu__link .kt-menu__link-text {
        font-size: 1.125rem;
        font-weight: 400;
    }
}

.state-active {
    background-color: #138135;
}

.state-draft {
    background-color: #FF8200;
}

.state-archived {
    background-color: #323232;
}

.state-cancelled {
    background-color: #FF0000;
}

.event .event-description {
    display: inline;
    margin-left: 10px;
}

.btn-outline-neutral {
    border-color: #AAAAAA;
}

.btn-outline-neutral:hover {
    background-color: #cccccc;
    border-color: #cccccc;
}

.btn [class^="la-"], .btn [class*=" la-"] {
    font-size: 1.8rem;
}

.text-success {
    color: #39B54A !important;
}

.btn-success {
    background-color: #39B54A;
}

.text-danger {
    color: #FF0000 !important;
}

.text-primary {
    color: #366CF3 !important;
}

#table-classroom-area th,
#table-classroom th {
    text-align: center;
}

#table-classroom th:nth-child(-n+4) {
    text-align: left;
}

#table-classroom th:nth-child(2),
#table-classroom th:nth-child(3) {
    text-align: left;
}

#table-classroom-area th:nth-child(-n+5) {
    text-align: left;
}

.kt-checkbox {
    font-size: 1.2rem;
}

.kt-checkbox > span {
    height: 20px;
    width: 20px;
    border-color: #48465B;
}

.kt-checkbox.kt-checkbox--bold > input:checked ~ span {
    border-color: #48465B;
}

.kt-checkbox > span:after {
    border-color: #48465B;
}

.nav-tabs.nav-tabs-line.nav-tabs-line-5x a.nav-link {
    border-bottom-width: 5px!important;
}

.nav-tabs.nav-tabs-line.nav-tabs-line-info.nav.nav-tabs .nav-link:hover, .nav-tabs.nav-tabs-line.nav-tabs-line-info.nav.nav-tabs .nav-link.active, .nav-tabs.nav-tabs-line.nav-tabs-line-info a.nav-link:hover, .nav-tabs.nav-tabs-line.nav-tabs-line-info a.nav-link.active {
    color: #366CF3;
    border-bottom: 1px solid #366CF3;
    font-weight: bold;
}

.btn-tag {
    padding: 0.5rem 0.7rem;
    line-height: 1;
}

.accordion.accordion-toggle-arrow .card .card-header .card-title:after {
    font-size: 2rem;
}

.kt-widget4 .kt-widget4__item {
    border-bottom-style: solid!important;
}

.progress {
    border-radius: 7px;
}

.progress-description .summary,
.progress-description .legend {
    font-size: 1.5rem;
    color: #646464;
}

.progress-description .legend {
    font-weight: 400;
}

.donut-chart text {
    fill: #646464;
}

/*
* Classrooms Cards::START
 */

.class-card {
    padding: 10px;
    background-color: white;
    border-left: 8px solid transparent;
    font-size: 1.125rem;
    margin-bottom: 20px;
    position: relative;
}

.class-card .status {
    position: absolute;
    right: 10px;
    top: 10px;
    font-size: 1.6rem;
}

.class-card .details {
    -webkit-transition:all linear 0.3s;
    transition:all linear 0.3s;
}

.class-card .details.ng-hide {
    opacity: 0;
}

.class-card p {
    margin-bottom: 0.5rem;
}

.class-card hr {
    border-color: #D8E1EF;
}

.class-card .footer {

}

.class-card.active {
    border-left: 8px solid #366CF3;
}

.bgr-very-light-gray {
    background-color: #F8F9FA;
}

.card-internal {
    border-radius: 10px;
    padding: 25px 30px;
    margin-top: 30px;
}

.card-internal:first-child {
    margin-top: 0;
}

.bgr-light-gray {
    background-color: #D8E1EF;
}

.clickable:hover {
    cursor: pointer;
    text-decoration: underline!important;
}

/*
* Classrooms Cards::END
 */

input.form-control:disabled {
    cursor: not-allowed!important;
    opacity: 0.6!important;
}

div.input-group-append:disabled {
    cursor: not-allowed!important;
    opacity: 0.6!important;
}

.no-animate {
    -webkit-transition: none !important;
    transition: none !important;
}

.form-control.area {
    max-width: 50px!important;
    text-align: center;
}

.nav-tabs.nav-tabs-line .nav-link {
    font-size: 1.2rem;
}

.kt-checkbox {
    font-size: 1.2rem;
}

.kt-checkbox > span {
    height: 20px;
    width: 20px;
    border-color: #48465B;
}

.kt-checkbox.kt-checkbox--bold > input:checked ~ span {
    border-color: #48465B;
}

.kt-checkbox > input:disabled ~ span:after {
    border-color: #48465B;
}

.kt-checkbox > span:after {
    border-color: #48465B;
}

.kt-checkbox > span:after {
}

.kt-radio > span {
    border-color: #48465B;
}

.kt-radio > input:checked ~ span {
    border-color: #48465B;
}

.kt-radio > span:after {
    border-color: #48465B;
}

.kt-radio.kt-radio--bold > input:checked ~ span {
    border-color: #48465B;
}

.btn.btn-label-direz {
    background-color: #006A53;
}

.btn.btn-label-area {
    background-color: #C7D300;
}

.btn-label-elearning {
    background-color: #4687E6;
}

.kt-portlet__head-label .btn.btn-pill {
    color: #ffffff;
    cursor: text !important;
    font-size: 1.5rem;
    padding: 0;
    height: 1.8rem;
    width: 1.8rem;
}

.btn.btn-mini-pill {
    color: #ffffff;
    cursor: text !important;
    font-size: 1em;
    border-radius: 1.6em;
    padding: 0.1em 0.5em;
}

.table.row-hover tr.user-warning td:first-child {
    border-left: 10px solid #ffb822 !important;
}

.table.row-hover tr.user-danger td:first-child {
    border-left: 10px solid #FF0000 !important;
}

/*
* Dots::START
 */

.dot-container {
    display: flex;
    flex-wrap: wrap;
}

.dot {
    display: block;
    float: left;
    border-radius: 10px;
    border: solid 2px black;
    width: 10px;
    height: 10px;
    margin: 1px;
}

.dot-full {
    border: solid 2px black;
    width: 10px;
    height: 10px;
    background-color: black;
}

.dot-full-special {
    background-color: orange;
    border-color: orange;
}
/*
* Dots::END
 */

/*
* Datpicker:START
 */

.uib-daypicker:focus {
    outline: none!important;
}

.uib-daypicker button {
    border: 0!important;
}

.uib-button-bar {
    display: none!important;
}
/*
* Datpicker:END
 */

.invalid-feedback {
    font-size: 100%;
}

/*
* Toastr Customization::START
 */
#toast-container > div {
    opacity: 1;
}

.toast-info .toast-title {
    color: #ffffff;
}
/*
* Toastr Customization::END
 */

/*
* Loader::START
 */

.tab-pane {
    position: relative;
}

.loader {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.6);
    z-index: 10;
}

.loader > div {
    display:flex;
    justify-content:center;
    align-items:center;
    height: 100%;
}

/*
* Loader::END
 */




/*
* Autocomplete::START
 */
.uib-typeahead-match.active a {
    color: #ffffff;
    background-color: #366cf3;
}
/*
* Autocomplete::END
 */




/*
* Dashboard::START
 */
.kt-portlet {
    border-radius: 10px;
}

.kt-portlet.signed {
    -webkit-box-shadow: 0px 0px 10px 10px rgba(230,230,230,1);
    -moz-box-shadow: 0px 0px 10px 10px rgba(230,230,230,1);
    box-shadow: 0px 0px 10px 10px rgba(230,230,230,1);
}

.kt-portlet.signed .kt-portlet__head {
    background-color: #00cca0;
}

.kt-portlet.new .kt-portlet__head {
    background-color: #00cca0;
}

.kt-portlet__body {
    position: relative;
}

.kt-portlet .kt-portlet__head {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.double-head {
    padding: 0 !important;
}

.kt-portlet.course .kt-portlet__head > div {
    width: 100%;
    justify-content: space-between;
    padding: 0 8px;
}

.kt-portlet.course .kt-portlet__head > div:first-child {
    border-top-left-radius: 10px;
}

.kt-portlet.course .kt-portlet__head > div:last-child {
    border-top-right-radius: 10px;
}

.kt-portlet.course .kt-portlet__head > div.signed {
    background-color: #00cca0;
}

.kt-portlet.course .kt-portlet__head > div.queued {
    background-color: #F15A24;
}

.kt-portlet.course .kt-portlet__head > div.booked {
    background-color: #006A53;
}

.kt-portlet.course .kt-portlet__head {
    color: white !important;
}

.kt-portlet .kt-portlet__head .kt-portlet__head-label .kt-portlet__head-icon {
    color: white;
}

.seats-count {
    font-size: 1.3em;
}

.seats-count strong {
    font-size: 1.2em;
}

/*.kt-portlet .kt-portlet__head .kt-portlet__head-label .kt-portlet__head-title {
    color: white;
}*/

.kt-widget17 .kt-widget17__stats .kt-widget17__items .kt-widget17__item {
    cursor: default;
}

.kt-widget17 .kt-widget17__stats .kt-widget17__items .kt-widget17__item .kt-widget17__subtitle {
    font-size: 1.6rem;
}

.kt-widget17 .kt-widget17__stats .kt-widget17__items .kt-widget17__item:hover {
    transition: all 0.3s ease;
    box-shadow: 0px 1px 15px 1px rgba(69, 65, 78, 0.06);
}

.item-fluid {
    padding: 1rem 2rem !important;
    font-size: 1.5rem;
    font-weight: 400;
}

.kt-portlet .kt-portlet__head .kt-portlet__head-label .kt-portlet__head-icon {
    font-size: 2rem;
}

.kt-portlet__body .description {
    margin: 0 auto;
    width: 90%;
    padding: 1rem 0.3rem 0rem;
    color: white;
    min-height: 175px;
    position: relative;
}

.kt-portlet__body .description .detail {
    position: absolute;
    bottom: 3rem;
}

.kt-portlet__body .description .detail h2 {
    font-size: 1.6rem;
    font-weight: 300;
    margin-bottom: 0;
}

.kt-portlet__body .description .detail span {
    font-size: 1.2rem;
}

.kt-portlet__body .description .title {
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 1.5rem;
}

.kt-widget17 .kt-widget17__stats {
    margin: -3rem auto 0 auto;
}

.kt-widget17 .kt-widget17__visual {
    background-image: url("../media/bg/KNPK1Zd.jpg");
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    border-radius: 0;
}

.kt-widget17 .kt-widget17__visual .overlay {
    background-color: rgba(0,0,0,0.3);
}

.kt-widget17 .card-action {
    width: 90%;
    margin: 0 auto;
    padding: 2rem 0.3rem;
}

.course .kt-portlet__head-actions .btn i {
    color: white;
}

.signed .btn-outline-light:active,
.signed .btn-outline-light:hover,
.waiting .btn-outline-light:active,
.waiting .btn-outline-light:hover {
    background-color: transparent; !important;
}

.waiting .btn-icon,
.signed .btn-icon {
    margin-right: 0.3rem;
}

.card-contextual-menu .btn.btn-clean i {
    color: #ffffff !important;
}

.card-contextual-menu.signed-list .btn.btn-clean:hover i {
    color: #00cca0 !important;
}

.waiting .btn-outline-light i,
.signed .btn-outline-light i {
    font-size: 1.4rem !important;
}

.card-contextual-menu {
    position: absolute;
    width: 100%;
    z-index: 10;
}

.card-contextual-menu.waiting-list {
    background-color: #F15A24;
}

.card-contextual-menu.signed-list {
    background-color: #00cca0;
}

.card-contextual-menu.booked-list {
    background-color: #006A53;
}

.card-contextual-menu .user-block {
    border-bottom: 1px solid white;
    padding: 0.2rem 0 0.2rem 0.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #ffffff;
    font-size: 1rem;
}

.icon-thicc {
    font-size: 1.8rem !important;
    font-weight: 600;
}

.portlet-simple {
    background-color: white;
    border-radius: 10px;
    padding: 25px 20px;
}

.portlet-simple .title {
    color: #006A53;
    font-size: 1.5rem;
    line-height: normal;
    font-weight: 400;
    margin-bottom: 30px;
    min-height: 75px;
}

.portlet-simple .detail {
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    border-bottom: 1px solid #D8E1EF;
    margin-bottom: 6px;
}

.portlet-simple .info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 6px;
    font-size: 1rem;
}

.portlet-simple .detail .icon-text {
    display: inline-flex;
    align-items: center;
    color: #006A53;
    font-weight: 500;
}

.portlet-simple .detail .icon-text i {
    font-size: 1.7rem;
}

.portlet-simple .detail .icon-text span {
    font-size: 1.2rem;
    padding-left: 5px;
}

.portlet-simple .action {
    display: flex;
    justify-content: space-between;
}

@media (max-width: 767px) {
    .kt-portlet .kt-portlet__head .kt-portlet__head-label .kt-portlet__head-icon {
        font-size: 1.5rem;
        padding-right: 0;
    }
    .kt-portlet__head-label .btn-pill {
        height: 1.8rem !important;
        width: 1.8rem !important;
        padding: 0;
    }
    .icon-thicc {
        font-size: 1.5rem!important;
    }
}

@media (max-width: 1024px) {
    body > header {
        margin-top: 60px;
    }
}
/*
* Daschboard::END
 */



/*
* Frontend::START
 */
.pill-label {
    width: 2rem;
    height: 2rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    border-radius: 50%;
}

.no-hover{
    pointer-events: none;
}

.pill-label.suspended {
    background-color: #F7931E;
}

.pill-label.signed {
    background-color: #006A53;
}

.pill-label.waiting {
    background-color: #F15A24;
}

.pill-label i {
    padding: 0;
    margin: 0;
    line-height: 0;
    font-size: 1.6rem;
    color: #ffffff;
}

.name-underline tr:hover > td:nth-child(3),
.name-underline tr:hover > td:nth-child(4) {
    text-decoration: underline;
}

#attendants-table.name-underline tr:hover > td:nth-child(2),
#attendants-table.name-underline tr:hover > td:nth-child(3) {
    text-decoration: underline;
}

#attendants-table.name-underline tr:hover > td:nth-child(4) {
    text-decoration: none;
}

.employeesTable {
    width: 100%;
}

.employeesTable thead {
    border-bottom: 1px solid;
}
.employeesTable th {
    font-size: 1.2em;
    font-weight: bold;
    padding: 0.5em;
}
.employeesTable td {
    padding: 0.5em;
    font-size: 1.2em;
}
/*
* Frontend::END
 */

/*
* Custom Colors::START
 */
.btn-queued {
    background-color: #F15A24;
    border-color: #F15A24;
    color: #fff;
}

.btn-booked {
    background-color: #006A53;
    border-color: #006A53;
    color: #fff;
}

.btn-signedup {
    background-color: #00cca0;
    border-color: #00cca0;
    color: #fff;
}
/*
* Custom Colors::END
 */

.signal {
    height: 50px;
    width: 50px;
    padding: 7px;
    background-color: transparent;
    border-radius: 50%;
    border: 1px solid #EBEDF2;
}

.signal.sm {
    height: 35px;
    width: 35px;
}

.signal > div {
    background-color: #E92525;
    height: 100%;
    width: 100%;
    border-radius: 50%;
}

.signal.active > div {
    background-color: #0DD114;
}

.status-card {
    transition: all 0.2s ease;
    font-weight: 400;
}

.status-card:hover {
    border: 1px solid darkgray;
    box-shadow: 0px 0px 15px 0px rgba(82 63 105, 20);
}

.status-card .ribbon {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 8px;
    border-radius: 5px 0 0 5px;
}

.custom-file-label:after {
    content: 'Upload File' !important;
}

.no-pointer-events {
    pointer-events: none;
}

.dropdown-menu > li > a, .dropdown-menu > .dropdown-item {
    white-space: pre;
}