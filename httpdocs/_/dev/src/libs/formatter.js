/**
 * Formats a given date string into a specified format.
 *
 * @param {string} date - The date string to be formatted.
 * @param {string} [type='both'] - The format type: 'date', 'time', or 'both'.
 * @returns {string} The formatted date string according to the specified type.
 */
export function formatDatetime(date, type = 'both') {
    const options = {
        date: { day: '2-digit', month: '2-digit', year: 'numeric' },
        time: { hour: '2-digit', minute: '2-digit' },
        both: {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        }
    };

    return new Intl.DateTimeFormat('it-IT', options[type]).format(new Date(date));
}

/**
 * Formats a date string into a specified format.
 *
 * @param {string} string - The date string to be formatted.
 * @param {Object|null} [options=null] - Optional formatting options for the date.
 *                                       Defaults to 'day', 'month', and 'year' in '2-digit' and 'numeric' formats.
 * @returns {string} - The formatted date string according to the specified or default options.
 */
export function formatDate(string, options = null) {
    debugger;
    const date = new Date(string);
    if (!options) {
        options = { day: '2-digit', month: '2-digit', year: 'numeric' };
    }
    return new Intl.DateTimeFormat('it-IT', options).format(date);
}

export function formatPrice(value)
{
    let val = (value / 1).toFixed(2).replace('.', ',');
    return val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".") + ' €';
}

export function formatNumber(
    num,
    options = {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    },
    locale = 'it') {
    return Number(num).toLocaleString(locale, options);
}

export function formatInteger(num, locale = 'it') {
    return formatNumber(num, { minimumFractionDigits: 0, maximumFractionDigits: 0 }, locale);
}

/**
 * Formats a numeric string as a currency in Euro.
 *
 * @param {string|number} string - The numeric value to be formatted.
 * @param {boolean} [showDecimals=true] - Whether to display decimal places.
 * @returns {string} The formatted currency string in Euro, using Italian locale.
 */
export function formatCurrency(string, showDecimals = true) {
    return new Intl.NumberFormat('it-IT', {
        style: 'currency',
        currency: 'EUR',
        minimumFractionDigits: showDecimals ? 2 : 0,
        maximumFractionDigits: showDecimals ? 2 : 0
    }).format(Number(string));
}
