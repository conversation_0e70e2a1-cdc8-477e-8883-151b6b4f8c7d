import {createRouter, createWebHistory} from 'vue-router'
import {isProduction} from '@/config';
import Team from "@/views/Team.vue";
import Home from "@/views/Home.vue";
import Courses from "@/views/Courses.vue";
import ActiveCourses from "@/views/ActiveCourses.vue";
import CourseDetail from "@/views/CourseDetail.vue";
import Bookings from "@/views/Bookings.vue";
import Materials from "@/views/materials/Materials.vue";
import Commercial from "@/views/materials/Commercial.vue";
import Description from "@/views/Description.vue";
import Products from "@/views/materials/Products.vue";
import Operative from "@/views/materials/Operative.vue";
import ActiveWebinars from "@/views/ActiveWebinars.vue";
import WebinarDetail from "@/views/WebinarDetail.vue";

const router = createRouter({
    linkExactActiveClass: 'active',
    history: createWebHistory('/uniqum/#!/'),
    routes: [
        {
            path: '/',
            name: 'home',
            component: Home,
            meta: { isHome: true }
        },
        {
            path: '/team',
            name: 'team',
            component: Team
        },
        /*{
            path: '/corsi',
            name: 'courses',
            component: Courses
        },*/
        /*{
            path: '/corsi-attivi',
            name: 'active-courses',
            component: ActiveCourses,
        },*/
        {
            path: '/corsi-attivi',
            name: 'active-webinars',
            component: ActiveWebinars,
        },
        {
            path: '/dettaglio/:id',
            name: 'course-detail',
            component: CourseDetail
        },
        {
            path: '/dettaglio-webinar/:id',
            name: 'webinar-detail',
            component: WebinarDetail,
        },
        {
            path: '/prenotazioni',
            name: 'bookings',
            component: Bookings
        },
        {
            path: '/materiali',
            name: 'materials',
            component: Materials,
            children: [
                {
                    path: 'ambito-commerciale',
                    name: 'commercial',
                    component: Commercial
                },
                {
                    path: 'conoscere-i-prodotti',
                    name: 'products',
                    component: Products
                },
                {
                    path: 'ambito-operativo-di-processo',
                    name: 'operative',
                    component: Operative
                },
            ]
        },
        {
            path: '/cos\'è-uniqum',
            name: 'description',
            component: Description
        }
    ]
})

export default router
