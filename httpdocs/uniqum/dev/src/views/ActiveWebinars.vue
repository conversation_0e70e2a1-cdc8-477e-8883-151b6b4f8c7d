<script setup>
import {ref} from "vue";
import {coursesApi} from "@/_common/api/courses.js";
import {toast} from "vue3-toastify";
import {CalendarDaysIcon} from "@heroicons/vue/24/outline/index.js";
import {Popover, PopoverButton, PopoverPanel} from "@headlessui/vue";

const courses = ref([])
let loading = ref(false)

refreshCourses()
async function refreshCourses() {
    loading.value = true;
    const res = await coursesApi.getActiveWebinars()
    loading.value = false;
    if ( ! res?.success ) {
        toast.error('Errore durante il caricamento dei corsi.')
        return;
    }
    courses.value = res.data
}
</script>

<template>
<!--    <div class="container-small mb-8">
        <h1 class="text-4xl font-bold mb-8">Corsi attivi</h1>
    </div>-->
    <div class="container-small">

        <div>
            <h1 class="text-4xl font-bold mb-5 pl-12">Corsi attivi</h1>
        </div>

        <div class="rounded-lg px-12 py-6" style="background-color: rgba(76,132,107,0.59); color: #FFFFFF">
            <div class="grid grid-cols-2 gap-5">
                <div class="col-span-2 md:col-span-1">
                    <p><strong>Modalità di partecipazione</strong></p>
                    <p>I corsi si svolgeranno in diretta streaming online nelle date previste.</p>
                    <p class="mb-5">Ogni partecipante può iscriversi a tutti e tre i corsi, ma non a più date dello stesso corso.</p>
                    <p><strong>Durata di ogni corso:</strong> 2 ore.</p>
                    <p class="mb-5"><strong>Numero partecipanti:</strong> massimo 20 per aula virtuale.</p>
                    <p><strong>Non sono previsti crediti formativi.</strong></p>
                </div>
                <div class="col-span-2 md:col-span-1">
                    <p><strong>Accesso alla piattaforma</strong></p>
                    <p class="mb-5">Per seguire i corsi sarà sufficiente collegarsi alla piattaforma e-learning disponibile su PortaleAgendo.</p>
                    <p><strong>Requisiti di partecipazione</strong></p>
                    <p>I webinar sono riservati esclusivamente ai dipendenti che hanno già frequentato le due aule in presenza nei mesi di gennaio-febbraio o aprile-maggio.</p>
                </div>
            </div>
        </div>

        <div class="px-12 py-6" v-if="!courses.length && !loading">
            <h1 class="text-xl font-semibold">Non sono attualmente presenti corsi attivi.</h1>
        </div>

        <div class="grid grid-cols-3 gap-6 py-8" v-if="loading">
            <div class="col-span-3 md:col-span-1">
                <div class="course-card">
                    <div class="animate-pulse">
                        <div class="bg-slate-200 rounded-t-xl w-full" style="min-height: 220px"></div>
                        <div class="card-body">
                            <div class="h-4 bg-slate-200 rounded mb-5"></div>
                            <div class="h-2 bg-slate-200 rounded mb-1"></div>
                            <div class="h-2 bg-slate-200 rounded mb-5"></div>
                            <div class="grid grid-cols-2 gap-3">
                                <div class="h-3 bg-slate-200 rounded"></div>
                                <div class="h-3 bg-slate-200 rounded"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-span-3 md:col-span-1">
                <div class="course-card">
                    <div class="animate-pulse">
                        <div class="bg-slate-200 rounded-t-xl w-full" style="min-height: 220px"></div>
                        <div class="card-body">
                            <div class="h-4 bg-slate-200 rounded mb-5"></div>
                            <div class="h-2 bg-slate-200 rounded mb-1"></div>
                            <div class="h-2 bg-slate-200 rounded mb-5"></div>
                            <div class="grid grid-cols-2 gap-3">
                                <div class="h-3 bg-slate-200 rounded"></div>
                                <div class="h-3 bg-slate-200 rounded"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-span-3 md:col-span-1">
                <div class="course-card">
                    <div class="animate-pulse">
                        <div class="bg-slate-200 rounded-t-xl w-full" style="min-height: 220px"></div>
                        <div class="card-body">
                            <div class="h-4 bg-slate-200 rounded mb-5"></div>
                            <div class="h-2 bg-slate-200 rounded mb-1"></div>
                            <div class="h-2 bg-slate-200 rounded mb-5"></div>
                            <div class="grid grid-cols-2 gap-3">
                                <div class="h-3 bg-slate-200 rounded"></div>
                                <div class="h-3 bg-slate-200 rounded"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-3 gap-6 py-8" v-if="courses.length && ! loading">

            <div class="col-span-3 md:col-span-1" v-for="course in courses">
                <div class="course-card overflow-hidden">
                    <div class="relative">
                        <img class="img-fluid w-full" :src="'https://' + window.staticHost + '/assets/apps/formazione/covers/' + course.cover" alt="">
                        <div class="absolute bottom-4 left-4 text-xs px-3 py-1 rounded-full bg-white">WEBINAR</div>
                    </div>
                    <div class="card-body">
                        <div class="font-bold leading-tight mb-5">{{course.title}}</div>
                        <div class="flex justify-end">
                            <RouterLink :to="{ name: 'webinar-detail', params: { id: course.course_id }}" class="btn btn-outline-red">Info e Prenotazione</RouterLink>
                        </div>
                    </div>
                </div>
            </div>

        </div>

    </div>
</template>

<style scoped>
.desc {
    background-color: rgba(11, 81, 60, 0.6);
    color: #FFFFFF;
}
table td {
    @apply px-2 py-0.5;
}
</style>