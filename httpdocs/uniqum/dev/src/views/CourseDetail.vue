<script setup>
import {useRoute} from "vue-router";
import {ref, useSlots} from "vue";
import {toast} from "vue3-toastify";
import {coursesApi} from "@/_common/api/courses.js";
import {Disclosure, DisclosureButton, DisclosurePanel, Tab, TabGroup, TabList, TabPanel, TabPanels} from "@headlessui/vue";
import {ChevronDownIcon} from "@heroicons/vue/16/solid/index.js";
import {bookingsApi} from "@/_common/api/bookings.js";
import ModalDialog from "@/views/ModalDialog.vue";
import {createConfirmDialog} from "vuejs-confirm-dialog";

const route = useRoute();
const courseId = route.params.id;
const course = ref({})
const classrooms = ref([])
const selectedTab = ref(0)
let classroomBookings = ref([])
let loadingBookingsData = ref(false)
const dialog = createConfirmDialog(ModalDialog, null, { chore: true, keepInitial: true })

getData()

async function getData() {
    const res = await coursesApi.getCourse(courseId)
    if ( ! res?.success ) {
        toast.error('Errore imprevisto.')
        return;
    }
    course.value = res.data;
    const res2 = await coursesApi.getAvailableClassrooms(courseId)
    if ( ! res2?.success ) {
        toast.error('Errore imprevisto.')
        return;
    }
    classrooms.value = res2.data.classrooms;
}
async function toggleClassroom(classroomId, index) {
    if (classrooms.value[index].isOpen) {
        classrooms.value[index].isOpen = false
        return
    }
    loadingBookingsData.value = true;
    classrooms.value.forEach((item) => {
        item.isOpen = false
    })
    classrooms.value[index].isOpen = true
    const res = await coursesApi.getClassroomBookings(classroomId)
    setTimeout(() => {
        loadingBookingsData.value = false;
        if ( ! res?.success ) {
            toast.error('Errore imprevisto.')
            return;
        }
        classroomBookings.value = res.data
    }, 500);
}

const onBookingClick = async (user, classroomIndex, classroom, bookingIndex) => {

    // Gestisce la modal e ritorna la risposta espressa dall'utente
    const modalAccepted = await handleModal(user,classroom)

    if (!modalAccepted) {
        return;
    }

    // Attiva il loader
    classroomBookings.value[bookingIndex].isLoading = true

    let bookingData = {
        classroomId: classroom.id,
        userId: user.id,
        // Seleziono la action in base al numero di posti disponibili
        action: (classroom.vacantSeats > 0) ? 'booked' : 'queued'
    }

    // Salvataggio prenotazione
    const res = await bookingsApi.bookUser(bookingData)
    processResponse(res, user, classroomIndex, classroom, bookingIndex, bookingData)

}

async function processResponse(res, user, classroomIndex, classroom, bookingIndex, bookingData) {
    classroomBookings.value[bookingIndex].isLoading = false

    // Se ho mandato richiesta di 'booked' e torna questo errore, allora l'aula è diventata piena
    if ( res?.errorCode === 118 ) {
        classroom.vacantSeats = 0;
        const modalAccepted = await handleModal(user, classroom)
        if (!modalAccepted) {
            return;
        }
        // Sovrascrivo la response
        res = await bookingsApi.bookUser(bookingData)
    }

    if ( ! res?.success ) {
        toast.error('Errore imprevisto.')
        return;
    }

    // Aggiorno lo stato delle prenotazioni
    classroomBookings.value = res.bookingStatus
    // Aggiorno i dati delle aule (principalmente per aggiornare i posti disponibili)
    classrooms.value[classroomIndex] = res.classroom
    classrooms.value[classroomIndex].isOpen = true
    sendBookingConfirmMessage(classroomBookings.value[bookingIndex])
}

async function handleModal(user, classroom) {
    let message = '';
    classroom.vacantSeats < 1 ?
        message = `I posti nell\'aula di ${classroom.city} del ${classroom.startDate} sono esauriti, proseguendo con la richiesta di prenotazione verrai messo in lista d\'attesa (sarai il numero ${classroom.queuedUsers + 1}). Vuoi comunque proseguire con la richiesta di prenotazione?`
        :
        message = `Sei sicuro di voler prenotare un posto per ${user.name} ${user.surname} nell\'aula di ${classroom.city} del ${classroom.startDate}?`


    // Modal di conferma
    const { data, isCanceled } = await dialog.reveal(
        {
            message: message,
        })

    // Ritorna falso se l'utente ha declinato, ritorna vero se l'utente ha dato consenso
    return !isCanceled;

}

async function sendBookingConfirmMessage(booking) {
    let message = "";
    switch (booking.state) {
        case 'booked':
            message = "La tua richiesta di prenotazione è stata presa in carico. Riceverai una email di conferma iscrizione a seguito di validazione da parte della Compagnia.";
            break;
        case 'queued':
            message = "La tua prenotazione in lista d'attesa è stata registrata. Riceverai una email di notifica qualora dovesse liberarsi un posto.";
            break;
    }
    const { data, isCanceled } = await dialog.reveal(
        {
            message: message,
            showConfirmButton: false,
            cancelButtonText: 'Ho capito'
        })
}

function getClassroomStatus (classroom) {
    var currentDate = new Date();
    var startDate = new Date(classroom.signupStartDate.split("/").reverse().join("-"));
    var endDate = new Date(classroom.signupEndDate.split("/").reverse().join("-"));

    if (currentDate >= startDate && currentDate <= endDate) {
        return 'active';
    }
    else if (currentDate > endDate) {
        return 'expired';
    }
    else if (currentDate < startDate) {
        return 'not started';
    }
}
</script>

<template>
    <div class="container-small pb-32">
        <div class="text-3xl font-bold" style="color: #00624A">{{course.title}}</div>
    </div>
    <div class="bg-slate-100 min-h-screen h-full py-16">
        <div class="container-small">
            <div class="card" style="margin-top: -180px">
                <TabGroup :defaultIndex="0" :selectedIndex="selectedTab">
                    <div class="card-header border-b mb-6">
                        <TabList class="flex gap-x-8 pb-5">
                            <Tab class="ui-selected:font-bold" @click="selectedTab = 0">Info generali</Tab>
                            <Tab class="ui-selected:font-bold" @click="selectedTab = 1">Prenotazione</Tab>
                        </TabList>
                    </div>
                    <div class="card-body">
                        <TabPanels>

                            <TabPanel>
                                <div class="grid grid-cols-10 gap-5">
                                    <div class="col-span-10 md:col-span-4">
                                        <img class="rounded-lg w-full mb-5" :alt="course.title" :src="'https://' + window.staticHost + '/assets/apps/formazione/covers/' + course.cover">
                                        <div class="text-center">
                                            <button class="btn btn-red" @click="selectedTab = 1">Prenota</button>
                                        </div>
                                    </div>
                                    <div class="col-span-10 md:col-span-6">
                                        <div class="text-lg font-bold">Tema del corso</div>
                                        <p class="mb-3">{{course.data?.description}}</p>
                                        <div class="text-lg font-bold">Programma 1a giornata</div>
                                        <div>09:30 -> 10:00: <span class="font-semibold">welcome caffè</span></div>
                                        <div>10:00 -> 13:00: <span class="font-semibold">lavori prima parte</span></div>
                                        <div>13:00 -> 14:00: <span class="font-semibold">pranzo</span></div>
                                        <div>14:00 -> 16:30: <span class="font-semibold">lavori seconda parte</span></div>
                                        <div class="mb-3">16:30 -> 17:00: <span class="font-semibold">chiusura lavori</span></div>
                                        <div class="text-lg font-bold">Programma 2a giornata</div>
                                        <div>09:30 -> 10:00: <span class="font-semibold">welcome caffè</span></div>
                                        <div>10:00 -> 13:00: <span class="font-semibold">lavori prima parte</span></div>
                                        <div>13:00 -> 14:00: <span class="font-semibold">pranzo</span></div>
                                        <div>14:00 -> 17:00: <span class="font-semibold">lavori seconda parte</span></div>
                                        <div class="mb-3">17:00 -> 17:30: <span class="font-semibold">erogazione test e consegna attestato</span></div>
                                        <!--<div v-for="event in course.data?.schedule">
                                            <div>{{event.startTime}} -> {{event.endTime}}: {{event.description}}</div>
                                        </div>-->
                                    </div>
                                </div>
                            </TabPanel>

                            <TabPanel>
                                <div class="bg-slate-100 rounded-xl mb-6" v-for="(classroom, classIndex) in classrooms">
                                    <div class="sm:flex items-center justify-between px-5 py-6 cursor-pointer" @click="toggleClassroom(classroom.id, classIndex)">
                                        <div class="font-medium text-lg">
                                            {{classroom.city}}
                                            <br>
                                            <template v-for="(day, index) in classroom.days">
                                                <span>{{day}} <span class="mr-1" v-if="index+1 < classroom.days.length">e</span></span>
                                            </template>
                                        </div>
                                        <div>
                                            <span v-if="getClassroomStatus(classroom) === 'active'">{{classroom.vacantSeats}} di {{classroom.totalSeats}}</span>
                                            <span class="btn-pill compact uppercase text-sm" v-if="getClassroomStatus(classroom) === 'expired' || getClassroomStatus(classroom) === 'not started'">prenotazioni chiuse</span>
                                            <ChevronDownIcon class="size-9 inline transition-all" :class="classroom.isOpen && 'rotate-180'" />
                                        </div>
                                    </div>
                                    <div class="border-t p-5 transition-all" v-if="classroom.isOpen">
                                        <div v-if="loadingBookingsData" class="text-center">
                                            <span class="loader"></span>
                                        </div>
                                        <div class="grid grid-cols-10 gap-5" v-if="!loadingBookingsData">
                                            <div class="col-span-10 sm:col-span-3">
                                                <div class="mb-5 leading-tight">
                                                    <div class="font-bold">Orario:</div>
                                                    <div>{{classroom.startHour}}</div>
                                                </div>
                                                <div class="mb-5 leading-tight">
                                                    <div class="font-bold">Luogo:</div>
                                                    <div>{{classroom.location}}</div>
                                                    <div>{{classroom.address}}</div>
                                                </div>
                                                <div class="mb-5 leading-tight">
                                                    <div class="font-bold">Apertura iscrizioni:</div>
                                                    <div>{{classroom.signupStartDate}}</div>
                                                </div>
                                                <div class="mb-5 leading-tight">
                                                    <div class="font-bold">Chiusura iscrizioni:</div>
                                                    <div>{{classroom.signupEndDate}}</div>
                                                </div>
                                            </div>
                                            <div class="col-span-10 sm:col-span-7">
                                                <div class="grid grid-cols-10 items-center px-1 py-3 border-b" v-for="(user, index) in classroomBookings">
                                                    <div class="col-span-10 sm:col-span-8 font-bold text-center sm:text-left">
                                                        {{user.name}} {{user.surname}}
                                                    </div>
                                                    <div class="col-span-10 sm:col-span-2 text-center">
                                                        <button v-if="!user.state && getClassroomStatus(classroom) === 'active'" type="button"
                                                                class="btn btn-red compact uppercase text-xs flex items-center gap-1"
                                                                @click="onBookingClick(user, classIndex, classroom, index)"
                                                                :disabled="user.isLoading"><span class="loader-mini" v-if="user.isLoading"></span> Prenota</button>
                                                        <span class="font-bold text-sm" v-if="user.state === 'signedup'">Iscritto</span>
                                                        <span class="font-bold text-sm" v-if="user.state === 'booked'">Prenotato</span>
                                                        <span class="font-bold text-sm" v-if="user.state === 'queued'">In lista d'attesa</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </TabPanel>

                        </TabPanels>
                    </div>
                </TabGroup>
            </div>
        </div>
    </div>
</template>

<style scoped>
.loader, .loader-mini {
    width: 80px;
    height: 80px;
    border: 5px solid #14513A;
    border-bottom-color: transparent;
    border-radius: 50%;
    display: inline-block;
    box-sizing: border-box;
    animation: rotation 1s linear infinite;
}
.loader-mini {
    width: 12px;
    height: 12px;
    border: 2px solid #FFFFFF;
    border-bottom-color: transparent;
}
@keyframes rotation {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
</style>