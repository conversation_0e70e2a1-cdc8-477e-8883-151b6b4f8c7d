<script setup>

import {ref} from "vue";
import {toast} from "vue3-toastify";
import {bookingsApi} from "@/_common/api/bookings.js";
import {formatDate} from "@/_common/formatter.js";
import {PlusCircleIcon} from "@heroicons/vue/24/solid/index.js";

const courses = ref([])
refreshBookings()
async function refreshBookings() {
    const res = await bookingsApi.currentBookings()
    if ( ! res?.success ) {
        toast.error('Errore durante il caricamento dei corsi.')
        return;
    }
    courses.value = res.data
}
function formatState(user) {
    // Se l'utente ha result 'ko', viene etichettato come 'No show' nell'applicativo
    if (user.result === 'ko') {
        return 'No show'
    }
    let formattedString = '';
    switch (user.state) {
        case 'signedup':
            formattedString = 'Iscritto'
            break;
        case 'booked':
            formattedString = 'Prenotato'
            break;
        case 'queued':
            formattedString = 'In lista d\'attesa'
            break;
    }
    return formattedString
}
</script>

<template>
    <div class="container-small pb-32">
        <div class="text-3xl font-bold" style="color: #00624A">Le tue prenotazioni</div>
    </div>
    <div class="container-small">
        <div class="card mb-5" v-for="course in courses">

            <template v-for="(classroom, index) in course.classrooms">
                <div class="grid grid-cols-10 gap-x-2 mb-3">
                    <div class="col-span-8 flex items-center gap-x-2">
                        <div>
                            <img class="rounded max-h-20" :alt="course.title" :src="'https://' + window.staticHost + '/assets/apps/formazione/covers/' + course.cover">
                        </div>
                        <div>
                            <div class="text-xl font-bold">{{course.title}}</div>
                            <div class="text-sm" style="color: #4E4E4E">{{classroom.city}} <span class="mr-1" v-for="(day, index) in classroom.data.days">{{formatDate(day.date)}} <span v-if="index+1 < classroom.data.days.length">e</span> </span></div>
                        </div>
                    </div>
                    <div class="col-span-2 flex items-center">
                        <RouterLink :to="{ name: 'course-detail', params: { id: course.id }}" class="btn btn-outline flex items-center gap-x-2">
                            <PlusCircleIcon class="size-6" />Prenota
                        </RouterLink>
                    </div>
                </div>
                <div class="card grid grid-cols-12 items-center mb-3" v-for="user in classroom.bookings">
                    <div class="col-span-10">
                        <div class="font-bold" style="color: #3E4154">{{user.nome}} {{user.cognome}}</div>
                    </div>
                    <div class="col-span-2">
                        <div class="text-center font-bold">{{formatState(user)}}</div>
                    </div>
                </div>
                <div class="py-6" v-if="index+1 !== course.classrooms.length">
                    <hr class="border-0 border-b">
                </div>
            </template>

        </div>
    </div>
</template>

<style scoped>

</style>