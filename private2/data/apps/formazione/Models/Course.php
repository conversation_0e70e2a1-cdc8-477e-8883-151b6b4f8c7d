<?php

namespace data\apps\formazione\Models;

use api\utils\ConvertToJson;
use data\apps\formazione\Models\Traits\GetPropertyFromData;

/**
 * @orm(source="vw_tra_course", target="tra_course")
 */
class Course implements \TrainingDev\Models\Interfaces\CourseInterface
{
    use \metadigit\core\db\orm\EntityTrait, ConvertToJson, GetPropertyFromData;

    /**
     * @orm(type="integer", primarykey, autoincrement)
     */
    protected $id;

    /**
     * @orm(type="string")
     */
    protected $title;

    /**
     * @orm(type="string")
     */
    protected $data;

    /**
     * @orm(type="string")
     * @validate(regex="/^(event|register)$/")
     */
    protected $type;

    /**
     * @orm(type="string")
     */
    protected $code;

    /**
     * @orm(type="float")
     */
    protected $credits;

    /**
     * @orm(type="string")
     * @validate(regex="/^(direz|area|e-learning)$/")
     */
    protected $groupamaType;

    /**
     * @orm(type="string")
     * @validate(regex="/^(on|off|ann|arc)$/")
     */
    protected $status;

    /**
     * @orm(type="string")
     */
    protected $year;

    /**
     * @orm(type="integer")
     */
    protected $ivass;

    /**
     * @orm(type="string")
     */
    protected $tag;

    /**
     * @orm(type="string")
     */
    protected $filters;

    /**
     * @orm(type="string")
     */
    protected $remoteId;

    /**
     * @orm(type="integer", readonly)
     */
    protected $class_id;

    /**
     * @orm(type="string")
     */
    protected $cover;

    /**
     * @orm(type="string", null)
     * @validate(null, regex="/^(NORM|TECN|FISC|ECON|SSPR|ALTRO|GIUR|TECA|ADMG|INFO|ACOP)$/")
     */
    protected $tipo;

    /**
     * @orm(type="string", null)
     * @validate(null, regex="/^(DIREZ|AGENTI|BANCHE|INTFNZ|SIM|POSTEIT|SOCFOR|ALTRO)$/")
     */
    protected $erogato;

    /**
     * @orm(type="string", null)
     * @validate(null, regex="/^(AULA|DIST)$/")
     */
    protected $modalita;

    /**
     * @orm(type="integer")
     */
    protected $gdpr;

    /**
     * @orm(type="integer")
     */
    protected $antiriciclaggio;

    /**
     * @orm(type="boolean")
     */
    protected $certificate;

    /**
     * @orm(type="boolean")
     */
    protected $dmAllowed;

    /**
     * @orm(type="integer")
     */
    protected $product_id;

    /**
     * @orm(type="string", readonly)
     */
    protected $productName;

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * @param mixed $title
     */
    public function setTitle($title)
    {
        $this->title = $title;
    }

    /**
     * @return mixed
     */
    public function getData()
    {
        return $this->data;
    }

    /**
     * @param mixed $data
     */
    public function setData($data)
    {
        $this->data = $data;
    }

    /**
     * @return mixed
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * @param mixed $type
     */
    public function setType($type)
    {
        $this->type = $type;
    }

    /**
     * @return mixed
     */
    public function getCode()
    {
        return $this->protocol;
    }

    /**
     * @param mixed $code
     */
    public function setCode($code)
    {
        $this->code = $code;
    }

    /**
     * @return mixed
     */
    public function getCredits()
    {
        return $this->credits;
    }

    /**
     * @param mixed $credits
     */
    public function setCredits($credits)
    {
        $this->credits = $credits;
    }

    /**
     * @return mixed
     */
    public function getGroupamaType()
    {
        return $this->groupamaType;
    }

    /**
     * @param mixed $groupamaType
     */
    public function setGroupamaType($groupamaType)
    {
        $this->groupamaType = $groupamaType;
    }

    /**
     * @return mixed
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @param mixed $status
     */
    public function setStatus($status)
    {
        $this->status = $status;
    }

    /**
     * @return mixed
     */
    public function getYear()
    {
        return $this->year;
    }

    /**
     * @param mixed $year
     */
    public function setYear($year)
    {
        $this->year = $year;
    }

    /**
     * @return mixed
     */
    public function getFilters()
    {
        return $this->filters;
    }

    /**
     * @param mixed $filters
     */
    public function setFilters($filters)
    {
        $this->filters = $filters;
    }

    /**
     * @return mixed
     */
    public function getRemoteId()
    {
        return $this->remoteId;
    }

    /**
     * @param mixed $remoteId
     */
    public function setRemoteId($remoteId)
    {
        $this->remoteId = $remoteId;
    }

    /**
     * @param mixed $cover
     */
    public function setCover($cover)
    {
        $this->cover = $cover;
    }

    /**
     * @return mixed
     */
    public function getCover()
    {
        return $this->cover;
    }

    /**
     * @return mixed
     */
    public function getTipo()
    {
        return $this->tipo;
    }

    /**
     * @param mixed $tipo
     */
    public function setTipo($tipo)
    {
        $this->tipo = $tipo;
    }

    /**
     * @return mixed
     */
    public function getErogato()
    {
        return $this->erogato;
    }

    /**
     * @param mixed $erogato
     */
    public function setErogato($erogato)
    {
        $this->erogato = $erogato;
    }

    /**
     * @return mixed
     */
    public function getModalita()
    {
        return $this->modalita;
    }

    /**
     * @param mixed $modalita
     */
    public function setModalita($modalita)
    {
        $this->modalita = $modalita;
    }

    /**
     * @return mixed
     */
    public function getGdpr()
    {
        return $this->gdpr;
    }

    /**
     * @param mixed $gdpr
     */
    public function setGdpr($gdpr)
    {
        $this->gdpr = $gdpr;
    }

    /**
     * @return mixed
     */
    public function getAntiriciclaggio()
    {
        return $this->antiriciclaggio;
    }

    /**
     * @param mixed $antiriciclaggio
     */
    public function setAntiriciclaggio($antiriciclaggio)
    {
        $this->antiriciclaggio = $antiriciclaggio;
    }

    /**
     * @return mixed
     */
    public function getCertificate()
    {
        return $this->certificate;
    }

    /**
     * @param mixed $certificate
     */
    public function setCertificate($certificate)
    {
        $this->certificate = $certificate;
    }

    /**
     * @return mixed
     */
    public function getDmAllowed()
    {
        return $this->dmAllowed;
    }

    /**
     * @param mixed $dmAllowed
     */
    public function setDmAllowed($dmAllowed)
    {
        $this->dmAllowed = $dmAllowed;
    }

    /**
     * @return mixed
     */
    public function getIvass()
    {
        return $this->ivass;
    }

    /**
     * @param mixed $ivass
     */
    public function setIvass($ivass)
    {
        $this->ivass = $ivass;
    }

    /**
     * @return mixed
     */
    public function getTag()
    {
        return $this->tag;
    }

    /**
     * @param mixed $tag
     */
    public function setTag($tag)
    {
        $this->tag = $tag;
    }

    /**
     * @return mixed
     */
    public function getProductName()
    {
        return $this->productName;
    }

    /**
     * @param mixed $productName
     */
    public function setProductName($productName)
    {
        $this->productName = $productName;
    }

    /**
     * @return mixed
     */
    public function getProductId()
    {
        return $this->product_id;
    }

    /**
     * @param mixed $product_id
     */
    public function setProductId($product_id)
    {
        $this->product_id = $product_id;
    }


    /**
     * PER IVASS
     */

    public function getTypology() {
        $data = self::getTypes();
        return $data[$this->tipo];
    }

    public function getDispensingStructure() {
        $data = self::getStructures();
        return $data[$this->erogato];
    }

    public function getMode() {
        $data = self::getModes();
        return $data[$this->modalita];
    }

    public static function getTypes() {
        return array (
            'NORM' => 'Normativo',
            'TECN' => 'Tecnico',
            'FISC' => 'Fiscale',
            'ECON' => 'Economico',
            'SSPR' => 'Su specifico prodotto',
            'ALTRO' => 'Altro',
            'ACOP' => 'Area contrattuale e prodotti',

            // Ivass 2015
            'GIUR' => 'Area giuridica',
            'TECA' => 'Area Tecnica assicurativa e riassicurativa',
            'ADMG' => 'Area amministrativa e gestionale',
            'INFO' => 'Area informatica',
        );
    }

    public static function getStructures() {
        return array (
            'DIREZ' => 'Direzione',
            'AGENTI' => 'Agenti',
            'BANCHE' => 'Banche',
            'INTFNZ' => 'Intermediari finanziari',
            'SIM' => 'Sim',
            'POSTEIT' => 'Poste italiane',
            'SOCFOR' => 'Società di formazione',
            'ALTRO' => 'Altro',
        );
    }

    public static function getModes() {
        return array (
            'AULA' => 'In aula',
            'DIST' => 'A distanza',
        );
    }
}
