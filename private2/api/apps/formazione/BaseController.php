<?php

namespace api\apps\formazione;


use metadigit\core\http\Request;
use metadigit\core\http\Response;
use TrainingDev\Traits\HandleTrait;

class BaseController extends \metadigit\core\web\controller\ActionController
{
    use HandleTrait;

    protected function preHandle(Request $Req, Response $Res)
    {
        if (! $access = $this->process($Req->getMethod(), $Req->URI(), $_SESSION['AUTH']['UTYPE'], $_SESSION['User'])) {
            return false;
        }

        return true;
    }

    protected function getRules()
    {
        return array(
            '*:/api/apps/formazione/*' => array(
                'resourceName' => NULL,
                'resourceId' => NULL,
                'grant' => array(
                    'roles' => array('KA', 'FORMAZIONE', 'AMMINISTRATORE', 'ASV'),
                    'users' => array(),
                ),
                'revoke' => array(
                    'roles' => array(),
                    'users' => array(),
                ),
                'menu' => array(
                    'grant' => array('*'),
                    'revoke' => array(),
                ),
            ),
            '*:/api/apps/formazione/course/*' => array(
                'resourceName' => NULL,
                'resourceId' => NULL,
                'grant' => array(
                    'roles' => array('AREAMGR', 'DISTRICTMGR', 'AREAMGR_COLLAB', 'ASV'),
                    'users' => array(),
                ),
                'revoke' => array(
                    'roles' => array(),
                    'users' => array(),
                ),
                'menu' => array(
                    'grant' => array('*'),
                    'revoke' => array(),
                ),
            ),
            '*:/api/apps/formazione/course/view/*' => array(
                'resourceName' => NULL,
                'resourceId' => NULL,
                'grant' => array(
                    'roles' => array('AGENTE', 'INTERMEDIARIO'),
                    'users' => array(),
                ),
                'revoke' => array(
                    'roles' => array(),
                    'users' => array(),
                ),
                'menu' => array(
                    'grant' => array('*'),
                    'revoke' => array(),
                ),
            ),
            '*:/api/apps/formazione/course/agencyCourses' => array(
                'resourceName' => NULL,
                'resourceId' => NULL,
                'grant' => array(
                    'roles' => array('AGENTE'),
                    'users' => array(),
                ),
                'revoke' => array(
                    'roles' => array(),
                    'users' => array(),
                ),
                'menu' => array(
                    'grant' => array('*'),
                    'revoke' => array(),
                ),
            ),
            '*:/api/apps/formazione/course/courseAttachments' => array(
                'resourceName' => NULL,
                'resourceId' => NULL,
                'grant' => array(
                    'roles' => array('AGENTE'),
                    'users' => array(),
                ),
                'revoke' => array(
                    'roles' => array(),
                    'users' => array(),
                ),
                'menu' => array(
                    'grant' => array('*'),
                    'revoke' => array(),
                ),
            ),
            '*:/api/apps/formazione/course/publicAttachments' => array(
                'resourceName' => NULL,
                'resourceId' => NULL,
                'grant' => array(
                    'roles' => array('AGENTE'),
                    'users' => array(),
                ),
                'revoke' => array(
                    'roles' => array(),
                    'users' => array(),
                ),
                'menu' => array(
                    'grant' => array('*'),
                    'revoke' => array(),
                ),
            ),
            '*:/api/apps/formazione/course/lastFetch' => array(
                'resourceName' => NULL,
                'resourceId' => NULL,
                'grant' => array(
                    'roles' => array('AGENTE', 'INTERMEDIARIO'),
                    'users' => array(),
                ),
                'revoke' => array(
                    'roles' => array(),
                    'users' => array(),
                ),
                'menu' => array(
                    'grant' => array('*'),
                    'revoke' => array(),
                ),
            ),
            '*:/api/apps/formazione/classroom/*/add' => array(
                'resourceName' => NULL,
                'resourceId' => NULL,
                'grant' => array(
                    'roles' => array(),
                    'users' => array(),
                ),
                'revoke' => array(
                    'roles' => array('AREAMGR', 'DISTRICTMGR', 'AREAMGR_COLLAB', 'ASV'),
                    'users' => array(),
                ),
                'menu' => array(
                    'grant' => array('*'),
                    'revoke' => array(),
                ),
            ),
            '*:/api/apps/formazione/classroom/*' => array(
                'resourceName' => NULL,
                'resourceId' => NULL,
                'grant' => array(
                    'roles' => array('AREAMGR', 'DISTRICTMGR', 'AREAMGR_COLLAB', 'ASV'),
                    'users' => array(),
                ),
                'revoke' => array(
                    'roles' => array(),
                    'users' => array(),
                ),
                'menu' => array(
                    'grant' => array('*'),
                    'revoke' => array(),
                ),
            ),
            '*:/api/apps/formazione/attendance/*' => array(
                'resourceName' => NULL,
                'resourceId' => NULL,
                'grant' => array(
                    'roles' => array('AREAMGR', 'DISTRICTMGR', 'AREAMGR_COLLAB', 'ASV'),
                    'users' => array(),
                ),
                'revoke' => array(
                    'roles' => array(),
                    'users' => array(),
                ),
                'menu' => array(
                    'grant' => array('*'),
                    'revoke' => array(),
                ),
            ),
            '*:/api/apps/formazione/attendance/*/users' => array(
                'resourceName' => NULL,
                'resourceId' => NULL,
                'grant' => array(
                    'roles' => array('AGENTE', 'INTERMEDIARIO'),
                    'users' => array(),
                ),
                'revoke' => array(
                    'roles' => array(),
                    'users' => array(),
                ),
                'menu' => array(
                    'grant' => array('*'),
                    'revoke' => array(),
                ),
            ),
            '*:/api/apps/formazione/attendance/*/user' => array(
                'resourceName' => NULL,
                'resourceId' => NULL,
                'grant' => array(
                    'roles' => array('AGENTE', 'INTERMEDIARIO'),
                    'users' => array(),
                ),
                'revoke' => array(
                    'roles' => array(),
                    'users' => array(),
                ),
                'menu' => array(
                    'grant' => array('*'),
                    'revoke' => array(),
                ),
            ),
            '*:/api/apps/formazione/attendance/agencyAttendees/*/course' => array(
                'resourceName' => NULL,
                'resourceId' => NULL,
                'grant' => array(
                    'roles' => array('AGENTE'),
                    'users' => array(),
                ),
                'revoke' => array(
                    'roles' => array(),
                    'users' => array(),
                ),
                'menu' => array(
                    'grant' => array('*'),
                    'revoke' => array(),
                ),
            ),
            '*:/api/apps/formazione/agencies/*' => array(
                'resourceName' => NULL,
                'resourceId' => NULL,
                'grant' => array(
                    'roles' => array('AREAMGR', 'DISTRICTMGR', 'AREAMGR_COLLAB', 'ASV', 'AGENTE'),
                    'users' => array(),
                ),
                'revoke' => array(
                    'roles' => array(),
                    'users' => array(),
                ),
                'menu' => array(
                    'grant' => array('*'),
                    'revoke' => array(),
                ),
            ),
            '*:/api/apps/formazione/users/*' => array(
                'resourceName' => NULL,
                'resourceId' => NULL,
                'grant' => array(
                    'roles' => array('AREAMGR', 'AREAMGR_COLLAB','DISTRICTMGR', 'ASV'),
                    'users' => array(),
                ),
                'revoke' => array(
                    'roles' => array(),
                    'users' => array(),
                ),
                'menu' => array(
                    'grant' => array('*'),
                    'revoke' => array(),
                ),
            ),
            '*:/api/apps/formazione/dashboard/*' => array(
                'resourceName' => NULL,
                'resourceId' => NULL,
                'grant' => array(
                    'roles' => array('AGENTE', 'INTERMEDIARIO'),
                    'users' => array(),
                ),
                'revoke' => array(
                    'roles' => array(),
                    'users' => array(),
                ),
                'menu' => array(
                    'grant' => array('*'),
                    'revoke' => array(),
                ),
            ),
            '*:/api/apps/formazione/certificate/*' => array(
                'resourceName' => NULL,
                'resourceId' => NULL,
                'grant' => array(
                    'roles' => array('AGENTE', 'DISTRICTMGR', 'AREAMGR'),
                    'users' => array(),
                ),
                'revoke' => array(
                    'roles' => array(),
                    'users' => array(),
                ),
                'menu' => array(
                    'grant' => array('*'),
                    'revoke' => array(),
                ),
            ),
            '*:/api/apps/formazione/booking/*' => array(
                'resourceName' => NULL,
                'resourceId' => NULL,
                'grant' => array(
                    'roles' => array('AGENTE'),
                    'users' => array(),
                ),
                'revoke' => array(
                    'roles' => array(),
                    'users' => array(),
                ),
                'menu' => array(
                    'grant' => array('*'),
                    'revoke' => array(),
                ),
            ),
            '*:/api/apps/formazione/booking/course/*/available' => array(
                'resourceName' => NULL,
                'resourceId' => NULL,
                'grant' => array(
                    'roles' => array('AGENTE', 'INTERMEDIARIO'),
                    'users' => array(),
                ),
                'revoke' => array(
                    'roles' => array(),
                    'users' => array(),
                ),
                'menu' => array(
                    'grant' => array('*'),
                    'revoke' => array(),
                ),
            ),
            '*:/api/apps/formazione/self-certificated/*' => array(
                'resourceName' => NULL,
                'resourceId' => NULL,
                'grant' => array(
                    'roles' => array('KA', 'FORMAZIONE', 'DISTRICTMGR', 'ASV', 'AGENTE', 'INTERMEDIARIO'),
                    'users' => array(),
                ),
                'revoke' => array(
                    'roles' => array(),
                    'users' => array(),
                ),
                'menu' => array(
                    'grant' => array('*'),
                    'revoke' => array(),
                ),
            ),
            '*:/api/apps/formazione/external/*' => array(
                'resourceName' => NULL,
                'resourceId' => NULL,
                'grant' => array(
                    'roles' => array('KA', 'FORMAZIONE', 'DISTRICTMGR', 'ASV', 'AGENTE', 'INTERMEDIARIO'),
                    'users' => array(),
                ),
                'revoke' => array(
                    'roles' => array(),
                    'users' => array(),
                ),
                'menu' => array(
                    'grant' => array('*'),
                    'revoke' => array(),
                ),
            ),
            '*:/api/apps/formazione/excel/course*' => array(
                'resourceName' => NULL,
                'resourceId' => NULL,
                'grant' => array(
                    'roles' => array('AREAMGR', 'AREAMGR_COLLAB', 'ASV'),
                    'users' => array(),
                ),
                'revoke' => array(
                    'roles' => array(),
                    'users' => array(),
                ),
                'menu' => array(
                    'grant' => array('*'),
                    'revoke' => array(),
                ),
            ),
            '*:/api/apps/formazione/excel/course-participants*' => array(
                'resourceName' => NULL,
                'resourceId' => NULL,
                'grant' => array(
                    'roles' => array('KA', 'FORMAZIONE', 'ASV'),
                    'users' => array(),
                ),
                'revoke' => array(
                    'roles' => array(),
                    'users' => array(),
                ),
                'menu' => array(
                    'grant' => array('*'),
                    'revoke' => array(),
                ),
            ),
            '*:/api/apps/formazione/course/summary*' => array(
                'resourceName' => NULL,
                'resourceId' => NULL,
                'grant' => array(
                    'roles' => array('KA', 'FORMAZIONE', 'ASV'),
                    'users' => array(),
                ),
                'revoke' => array(
                    'roles' => array(),
                    'users' => array(),
                ),
                'menu' => array(
                    'grant' => array('*'),
                    'revoke' => array(),
                ),
            ),
            '*:/api/apps/formazione/verification/*' => array(
                'resourceName' => NULL,
                'resourceId' => NULL,
                'grant' => array(
                    'roles' => array('KA', 'FORMAZIONE', 'DISTRICTMGR', 'ASV'),
                    'users' => array(),
                ),
                'revoke' => array(
                    'roles' => array(),
                    'users' => array(),
                ),
                'menu' => array(
                    'grant' => array('*'),
                    'revoke' => array(),
                ),
            ),
            '*:/api/apps/formazione/excel/verifications/*' => array(
                'resourceName' => NULL,
                'resourceId' => NULL,
                'grant' => array(
                    'roles' => array('KA', 'FORMAZIONE', 'DISTRICTMGR', 'ASV'),
                    'users' => array(),
                ),
                'revoke' => array(
                    'roles' => array(),
                    'users' => array(),
                ),
                'menu' => array(
                    'grant' => array('*'),
                    'revoke' => array(),
                ),
            ),
            '*:/api/apps/formazione/assinform/*' => array(
                'resourceName' => NULL,
                'resourceId' => NULL,
                'grant' => array(
                    'roles' => array('AGENTE'),
                    'users' => array(),
                ),
                'revoke' => array(
                    'roles' => array(),
                    'users' => array(),
                ),
                'menu' => array(
                    'grant' => array('*'),
                    'revoke' => array(),
                ),
            ),
            '*:/api/apps/formazione/products/*' => array(
                'resourceName' => NULL,
                'resourceId' => NULL,
                'grant' => array(
                    'roles' => array('KA', 'FORMAZIONE', 'DISTRICTMGR', 'ASV', 'AREAMGR', 'DISTRICTMGR'),
                ),
            ),
            '*:/api/apps/formazione/neofiti/*' => array(
                'resourceName' => NULL,
                'resourceId' => NULL,
                'grant' => array(
                    'roles' => array('AGENTE'),
                    'users' => array(),
                ),
                'revoke' => array(
                    'roles' => array(),
                    'users' => array(),
                ),
                'menu' => array(
                    'grant' => array('*'),
                    'revoke' => array(),
                ),
            ),
            '*:/api/apps/formazione/survey/*' => array(
                'resourceName' => NULL,
                'resourceId' => NULL,
                'grant' => array(
                    'roles' => array('AGENTE'),
                    'users' => array(),
                ),
                'revoke' => array(
                    'roles' => array(),
                    'users' => array(),
                ),
                'menu' => array(
                    'grant' => array('*'),
                    'revoke' => array(),
                ),
            ),
            '*:/api/apps/formazione/file/*' => array(
                'resourceName' => NULL,
                'resourceId' => NULL,
                'grant' => array(
                    'roles' => array('KA', 'FORMAZIONE', 'AREAMGR', 'DISTRICTMGR', 'ASV'),
                    'users' => array(),
                ),
                'revoke' => array(
                    'roles' => array(),
                    'users' => array(),
                ),
                'menu' => array(
                    'grant' => array('*'),
                    'revoke' => array(),
                ),
            ),
        );
    }
}
