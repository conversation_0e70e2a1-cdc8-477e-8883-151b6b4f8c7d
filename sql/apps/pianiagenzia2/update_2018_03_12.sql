
CREATE TABLE `pianiag2_piani_areeBisogno` (
  `piano_id` int(10) UNSIGNED NOT NULL,
  `areaBisogno_id` int(10) UNSIGNED NOT NULL,
  `predefinita` tinyint(4) NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE `pianiag2_piani_areeBisogno`
  ADD PRIMARY KEY (`piano_id`,`areaBisogno_id`) USING BTREE,
  ADD KEY `fk_pianiag2_piani_areeBisogno__areaBisogno_id` (`areaBisogno_id`);

ALTER TABLE `pianiag2_piani_areeBisogno`
  ADD CONSTRAINT `fk_pianiag2_piani_areeBisogno__agenzia_id` FOREIGN KEY (`piano_id`) REFERENCES `pianiag2_piani` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_pianiag2_piani_areeBisogno__areaBisogno_id` FOREIGN KEY (`areaBisogno_id`) REFERENCES `pianiag2_areeBisogno` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;


INSERT INTO pianiag2_piani_areeBisogno(piano_id, areaBisogno_id, predefinita)
SELECT (select max(id) from pianiag2_piani p where p.agenzia_id = aab.agenzia_id) as piano_id, areaBisogno_id, predefinita
FROM pianiag2_agenzie_areeBisogno aab;


DROP TABLE pianiag2_agenzie_areeBisogno;



ALTER TABLE `pianiag2_azioni` ADD `piano_id` INT UNSIGNED NULL AFTER `agenzia_id`;
ALTER TABLE `pianiag2_azioni` ADD INDEX(`piano_id`);
ALTER TABLE `pianiag2_azioni` ADD CONSTRAINT `fk_pianiag2_azioni__piani_id` FOREIGN KEY (`piano_id`) REFERENCES `pianiag2_piani`(`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;
UPDATE pianiag2_azioni a SET piano_id = (select max(id) from pianiag2_piani p where p.agenzia_id = a.agenzia_id);
ALTER TABLE `pianiag2_azioni` DROP FOREIGN KEY fk_pianiag2_azioni__agenzia_id;
ALTER TABLE `pianiag2_azioni` DROP COLUMN agenzia_id;
