DROP TABLE IF EXISTS `incentive`;
CREATE TABLE `incentive` (
  `id` smallint(5) UNSIGNED NOT NULL,
  `program` varchar(64) NOT NULL,
  `name` varchar(32) NOT NULL,
  `status` enum('inactive','started','active','closed','liquidated') NOT NULL,
  `start` date NOT NULL,
  `end` date NOT NULL,
  `lastUpdate` datetime NOT NULL,
  `lastUpdateLabel` date NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Indici per le tabelle scaricate
--

--
-- Indici per le tabelle `incentive`
--
ALTER TABLE `incentive`
ADD PRIMARY KEY (`id`);

ALTER TABLE `incentive`
MODIFY `id` smallint(5) UNSIGNED NOT NULL AUTO_INCREMENT;

DROP TABLE IF EXISTS `inc_rinn_data`;
CREATE TABLE `inc_rinn_data` (
  `iniziativa_id` smallint(5) UNSIGNED NOT NULL,
  `agenzia_id` char(4) NOT NULL,
  `policy` varchar(64) NOT NULL,
  `renewal` tinyint(1) NOT NULL,
  `increase` decimal(5,2) NOT NULL,
  `premiumCurrent` float NOT NULL,
  `premiumPrevious` float NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Indici per le tabelle scaricate
--

--
-- Indici per le tabelle `inc_rinn_data`
--
ALTER TABLE `inc_rinn_data`
ADD PRIMARY KEY (`iniziativa_id`,`agenzia_id`,`policy`),
ADD KEY `agenzia_id` (`agenzia_id`),
ADD KEY `iniziativa_id` (`iniziativa_id`);

--
-- Limiti per le tabelle scaricate
--

--
-- Limiti per la tabella `inc_rinn_data`
--
ALTER TABLE `inc_rinn_data`
ADD CONSTRAINT `inc_rinn_data_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`),
ADD CONSTRAINT `inc_rinn_data_ibfk_2` FOREIGN KEY (`iniziativa_id`) REFERENCES `incentive` (`id`);

DROP TABLE IF EXISTS `inc_rinn_status`;
CREATE TABLE `inc_rinn_status` (
  `iniziativa_id` smallint(5) UNSIGNED NOT NULL,
  `agenzia_id` char(4) NOT NULL,
  `suitableRatio` float NOT NULL,
  `portfolioRatio` float NOT NULL,
  `portfolioPerformance` float NOT NULL,
  `portfolioValue` float NOT NULL,
  `premium` float NOT NULL,
  `bonus` float NOT NULL,
  `L1suitable` float NOT NULL,
  `L1suitableRatio` float NOT NULL,
  `L1bonus` float NOT NULL,
  `L2suitable` float NOT NULL,
  `L2suitableRatio` float NOT NULL,
  `L2bonus` float NOT NULL,
  `L3suitable` float NOT NULL,
  `L3suitableRatio` float NOT NULL,
  `L3bonus` float NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Indici per le tabelle `inc_rinn_status`
--
ALTER TABLE `inc_rinn_status`
ADD PRIMARY KEY (`iniziativa_id`,`agenzia_id`),
ADD KEY `agenzia_id` (`agenzia_id`),
ADD KEY `iniziativa_id` (`iniziativa_id`);

--
-- Limiti per la tabella `inc_rinn_status`
--
ALTER TABLE `inc_rinn_status`
ADD CONSTRAINT `inc_rinn_status_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`),
ADD CONSTRAINT `inc_rinn_status_ibfk_2` FOREIGN KEY (`iniziativa_id`) REFERENCES `incentive` (`id`);

# CREATE TABLE `inc_rinn_static` (
#   `iniziativa_id` smallint(10) UNSIGNED NOT NULL,
#   `agenzia_id` char(4) NOT NULL,
#   `active` tinyint(1) NOT NULL
# ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
#
# --
# -- Indici per le tabelle `inc_rinn_static`
# --
# ALTER TABLE `inc_rinn_static`
# ADD PRIMARY KEY (`iniziativa_id`,`agenzia_id`),
# ADD KEY `agenzia_id` (`agenzia_id`);
#
# --
# -- Limiti per la tabella `inc_rinn_static`
# --
# ALTER TABLE `inc_rinn_static`
# ADD CONSTRAINT `inc_rinn_static_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`),
# ADD CONSTRAINT `inc_rinn_static_ibfk_2` FOREIGN KEY (`iniziativa_id`) REFERENCES `incentive` (`id`);

drop view if exists vw_inc_rinn_aggregate;
create view vw_inc_rinn_aggregate as
  SELECT
    iniziativa_id,
    agenzia_id,
    a.district,

-- percentuale di rinnovi su totale
    sum(renewal=1)/count(1) as renewalRatio,

-- totale rinnovi
    sum(renewal) as renewalTot,

-- totale rinnovi con almeno +1.5%
    sum(
        case
        when renewal = 1 then (premiumCurrent/premiumPrevious) >= 1.015
        else 0
        end
    ) as suitableTot,

    sum(
        case
        when renewal = 1 then premiumCurrent
        else 0
        end
    ) as premiumCurrentRenewal,

    sum(
        case
        when renewal = 1 then premiumPrevious
        else 0
        end
    ) as premiumPreviousRenewal,

    sum(premiumCurrent * renewal) as premiumTot

  FROM
    `inc_rinn_data`
  JOIN agenzie a ON agenzia_id = a.id
  group by agenzia_id, iniziativa_id;

drop view if exists vw_inc_rinn_status;
create view vw_inc_rinn_status as
  SELECT
    a.area,
    ar.nome as nomeArea,
    a.district,
    d.nome as nomeDistrict,
    a.localita as agencyName,
    s.*
  FROM `inc_rinn_status` s
  join agenzie a on a.id = s.agenzia_id
  join geo_aree ar on ar.id = a.area
  join geo_districts d on d.id = a.district;

drop view if exists vw_inc_rinn_periods;
create view vw_inc_rinn_periods as SELECT
  i.id,
  a.id as agenzia_id,
  a.area,
  a.district,
  s.bonus, s.suitableRatio,
  i.program, i.name, i.status

FROM incentive i

  join inc_rinn_status s on s.iniziativa_id = i.id
  join agenzie a on a.id = s.agenzia_id;

drop view if exists vw_inc_rinn_monitoring_area;
create view vw_inc_rinn_monitoring_area as SELECT
s.iniziativa_id,
a.area,
ar.nome as name,
round( sum(suitableRatio >= 30) / count(agenzia_id) * 100 ) as suitableRatio,
round( sum(portfolioValue >= 0.8) / count(agenzia_id) * 100 ) as portfolioValue,
sum(bonus) as bonus
FROM inc_rinn_status s
join agenzie a on a.id = s.agenzia_id
  join geo_aree ar on ar.id = a.area
  join geo_districts d on d.id = a.district

group by area, s.iniziativa_id;

drop view if exists vw_inc_rinn_monitoring_district;
create view vw_inc_rinn_monitoring_district as SELECT
s.iniziativa_id,
a.area,
  ar.nome as nomeArea,
a.district,
  d.nome as name,
round( sum(suitableRatio >= 30) / count(agenzia_id) * 100 ) as suitableRatio,
round( sum(portfolioValue >= 0.8) / count(agenzia_id) * 100 ) as portfolioValue,
sum(bonus) as bonus
FROM inc_rinn_status s
join agenzie a on a.id = s.agenzia_id
  join geo_aree ar on ar.id = a.area
  join geo_districts d on d.id = a.district

group by district, s.iniziativa_id;

/* Patch per aggiungere data di incasso */

ALTER TABLE `inc_rinn_data` ADD `collectionDate` DATE NOT NULL;