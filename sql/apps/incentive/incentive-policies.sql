DROP TABLE IF EXISTS incentive_data;
CREATE TABLE `incentive_data` (
    `ptf` enum('D','V') NOT NULL,
    `incentive_id` smallint UNSIGNED NOT NULL,
    `dataEstrazione` date NOT NULL,
    `dataEmissione` date NOT NULL,
    `dataIncasso` date NOT NULL,
    `dataEffetto` date NOT NULL,
    `agenzia_id` char(4) NOT NULL,
    `ramo` varchar(2) NOT NULL,
    `intermediario` varchar(5) NOT NULL,
    `delega`  enum('1','2','3') NOT NULL,
    `sezione` varchar(2) NOT NULL,
    `polizza` varchar(20) NOT NULL,
    `codiceIdBene` varchar(10) NOT NULL,
    `nomeCliente` varchar(50) NOT NULL,
    `codiceCliente` varchar(10) NOT NULL,
    `CF_PIVA` varchar(16) NOT NULL,
    `codiceProdotto` varchar(6) NOT NULL,
    `motivo` ENUM('N','S','T','R','V','U') NOT NULL,
    `famigliaProdotto`  varchar(10) NOT NULL,
    `premioAnnuo` FLOAT NOT NULL,
    `premioUnico` FLOAT NOT NULL,
    `versamAgg` FLOAT NOT NULL,
    `deltaPremio` FLOAT NOT NULL,
    `premioComputabile` FLOAT NOT NULL,
    `codiceCampagna` varchar(10) NOT NULL,
    `codiceConvenzione` varchar(10) NOT NULL,
    `codicePartnership` varchar(10) NOT NULL,
    `tipoCollettiva` varchar(2) NOT NULL,
    `numPolizzaMadre` varchar(20) NOT NULL,
    `modulareVita` varchar(2) NOT NULL,
    `OTP` varchar(2) NOT NULL,
    `FEA` varchar(2) NOT NULL,
    `trasmissioneElettr` varchar(2) NOT NULL,
    `polizzaDigitale` varchar(2) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='Polizze';

--
-- Indici per le tabelle scaricate
--

--
-- Indici per le tabelle `incentive_data`
--
ALTER TABLE `incentive_data`
    ADD PRIMARY KEY (`incentive_id`, `agenzia_id`, `polizza`, `ramo`, `motivo`, `dataEmissione`, `famigliaProdotto`),
    ADD KEY `fk_incentive` (`incentive_id`),
    ADD KEY `fk_agenzie` (`agenzia_id`);

--
-- Limiti per le tabelle scaricate
--

--
-- Limiti per la tabella `incentive_data`
--
ALTER TABLE `incentive_data`
    ADD CONSTRAINT `fk_incentive_data__iniz_id` FOREIGN KEY (`incentive_id`) REFERENCES `incentive` (`id`),
    ADD CONSTRAINT `fk_incentive_data_agenzia_id` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`);