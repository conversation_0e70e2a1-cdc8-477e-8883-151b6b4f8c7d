drop table if exists inc_fullprotection_2024_static;
CREATE TABLE `inc_fullprotection_2024_static` (
  `agenzia_id` char(4) NOT NULL primary key,
  `active` tinyint(1) NOT NULL/*,
  `objPezziTot` tinyint unsigned NOT NULL,
  `objInf` tinyint unsigned NOT NULL*/
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Limiti per la tabella `inc_fullprotection_2024_static`
--
ALTER TABLE `inc_fullprotection_2024_static`
  ADD CONSTRAINT `inc_fullprotection_2024_static_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`);

--
-- Struttura della tabella `inc_fullprotection_2024_data`
--
drop table if exists inc_fullprotection_2024_data;
CREATE TABLE `inc_fullprotection_2024_data` (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT primary key,
  `agenzia_id` char(4) NOT NULL,
  `policyNumber` varchar(64) NOT NULL,
  `product` varchar(6) NOT NULL,
  `premium` float NOT NULL,
  `new` tinyint(1) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Limiti per la tabella `inc_fullprotection_2024_data`
--
ALTER TABLE `inc_fullprotection_2024_data`
  ADD CONSTRAINT `inc_fullprotection_2024_data_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`);

--
-- Struttura della tabella `inc_fullprotection_2024_status`
--
drop table if exists inc_fullprotection_2024_status;
CREATE TABLE `inc_fullprotection_2024_status` (
  `agenzia_id` char(4) NOT NULL primary key,
  `pezziTot` smallint UNSIGNED NOT NULL,
  `pezziINF` smallint UNSIGNED NOT NULL,
  `premiumFascia1` float NOT NULL,
  `premiumFascia2` float NOT NULL,
  `incentFascia1` float NOT NULL,
  `incentFascia2` float NOT NULL,
  `incentTotal` float NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Limiti per la tabella `inc_fullprotection_2024_status`
--
ALTER TABLE `inc_fullprotection_2024_status`
    ADD CONSTRAINT `inc_fullprotection_2024_status_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`);

DROP VIEW IF EXISTS vw_inc_fullprotection_2024_status;
CREATE VIEW vw_inc_fullprotection_2024_status AS (
    SELECT s.agenzia_id, s.pezziTot, s.pezziINF, s.premiumFascia1, s.premiumFascia2, s.incentFascia1, s.incentFascia2, s.incentTotal, ag.localita as localita, ag.nome as nome, ga.nome as areaName, gd.nome as districtName, ga.id as area, gd.id as district FROM inc_fullprotection_2024_status s
JOIN agenzie ag ON ag.id = s.agenzia_id
JOIN geo_aree ga ON ga.id = ag.area
JOIN geo_districts gd ON ag.district = gd.id);