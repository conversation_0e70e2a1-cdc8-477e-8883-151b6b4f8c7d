--
-- <PERSON><PERSON><PERSON><PERSON> della tabella `inc_compass_2025_status`
--
DROP TABLE IF EXISTS `inc_compass_2025_status`;
CREATE TABLE `inc_compass_2025_status` (
    `agenzia_id` char(4) NOT NULL,
    `fascia` enum('M', 'L', 'XL', 'NEOMANDATI') NOT NULL,
    `prestitiPers` SMALLINT unsigned NOT NULL,
    `punteggioPrestitiPers` MEDIUMINT unsigned NOT NULL,
    `nuovoStoreCredit` SMALLINT unsigned NOT NULL,
    `punteggioNuovoStoreCredit` MEDIUMINT unsigned NOT NULL,
    `prestitiFinal` SMALLINT unsigned NOT NULL,
    `punteggioPrestitiFinal` MEDIUMINT unsigned NOT NULL,
    `riutilizzoStoreCredit` SMALLINT unsigned NOT NULL,
    `punteggioRiutilizzoStoreCredit` MEDIUMINT unsigned NOT NULL,
    `punteggio` MEDIUMINT unsigned NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Indici per le tabelle `inc_compass_2025_status`
--
ALTER TABLE `inc_compass_2025_status`
    ADD PRIMARY KEY (`agenzia_id`);

--
-- Limiti per la tabella `inc_compass_2025_status`
--
ALTER TABLE `inc_compass_2025_status`
    ADD CONSTRAINT `inc_compass_2025_status_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`);

drop view if exists vw_inc_compass_2025_status;
create view vw_inc_compass_2025_status as
SELECT s.agenzia_id, 
       a.localita, 
       a.area,
       ga.nome as areaName,
       a.district,
       gd.nome as districtName,
       s.fascia, 
       s.prestitiPers, 
       s.punteggioPrestitiPers, 
       s.nuovoStoreCredit, 
       s.punteggioNuovoStoreCredit, 
       s.prestitiFinal, 
       s.punteggioPrestitiFinal, 
       s.riutilizzoStoreCredit, 
       s.punteggioRiutilizzoStoreCredit, 
       s.punteggio
FROM inc_compass_2025_status s
JOIN agenzie a ON a.id = s.agenzia_id
JOIN geo_aree ga ON ga.id = a.area
JOIN geo_districts gd ON gd.id = a.district
