drop table if exists inc_fullbenessere_2024_static;
CREATE TABLE `inc_fullbenessere_2024_static` (
  `agenzia_id` char(4) NOT NULL primary key,
  `pezziAnnoPrec` smallint UNSIGNED NOT NULL,
  `gruppo` enum('A','B','C','D','E') NOT NULL,
  `obiettivo` tinyint(1) NOT NULL,
  `etaMedia` tinyint(2) NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- <PERSON>iti per la tabella `inc_fullbenessere_2024_static`
--
ALTER TABLE `inc_fullbenessere_2024_static`
  ADD CONSTRAINT `inc_fullbenessere_2024_static_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`);

--
-- Strutt<PERSON> della tabella `inc_fullbenessere_2024_data`
--
drop table if exists inc_fullbenessere_2024_data;
CREATE TABLE `inc_fullbenessere_2024_data` (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT primary key,
  `agenzia_id` char(4) NOT NULL,
  `policyNumber` varchar(64) NOT NULL,
  `product` varchar(6) NOT NULL,
  `premium` float NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Limiti per la tabella `inc_fullbenessere_2024_data`
--
ALTER TABLE `inc_fullbenessere_2024_data`
  ADD CONSTRAINT `inc_fullbenessere_2024_data_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`);

--
-- Struttura della tabella `inc_fullbenessere_2024_status`
--
drop table if exists inc_fullbenessere_2024_status;
CREATE TABLE `inc_fullbenessere_2024_status` (
  `agenzia_id` char(4) NOT NULL primary key,
  `pezzi` smallint UNSIGNED NOT NULL,
  `premio` float NOT NULL,
  `incent` float NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Limiti per la tabella `inc_fullbenessere_2024_status`
--
ALTER TABLE `inc_fullbenessere_2024_status`
    ADD CONSTRAINT `inc_fullbenessere_2024_status_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`);

DROP VIEW IF EXISTS vw_inc_fullbenessere_2024_status;
CREATE VIEW vw_inc_fullbenessere_2024_status AS (
    SELECT s.agenzia_id, s.pezzi, s.premio, s.incent, ic.etaMedia, ic.obiettivo, ag.localita as localita, ag.nome as nome, ga.nome as areaName, gd.nome as districtName, ga.id as area, gd.id as district FROM inc_fullbenessere_2024_status s
JOIN agenzie ag ON ag.id = s.agenzia_id
JOIN inc_fullbenessere_2024_static ic ON ic.agenzia_id = s.agenzia_id
JOIN geo_aree ga ON ga.id = ag.area
JOIN geo_districts gd ON ag.district = gd.id);