DROP TABLE IF EXISTS life_data;
CREATE TABLE `life_data` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `periodo` VARCHAR(20) NULL,
  `dataOsservazione` DATE NULL,
  `agenzia_id` CHAR(4) NOT NULL,
  `subagenziaDett` VARCHAR(20) NULL,
  `produttoreDett` VARCHAR(20) NULL,
  `polizza` VARCHAR(20) NOT NULL,
  `codiceProdotto` VARCHAR(6) NOT NULL,
  `nomeProdotto` VARCHAR(50) NOT NULL,
  `CF_PIVA` VARCHAR(64) NOT NULL,
  `dataContabile` DATE NOT NULL,
  `dataScadenza` DATE NOT NULL,
  `dataEffetto` DATE NULL,
  `dataEmissione` DATE NOT NULL,
  `dataIncasso` DATE NULL,
  `tipoPortafoglio` VARCHAR(6) NULL,
  `tipoPremioQuietanza` VARCHAR(50) NOT NULL,
  `tipoPremioPolizza` VARCHAR(50) NOT NULL,
  `codiceConvenzione` VARCHAR(50) NULL,
  `percNoUnit` TINYINT UNSIGNED NULL,
  `percUnit` TINYINT UNSIGNED NULL,
  `frazionamento` VARCHAR(50) NULL,
  `premio` DECIMAL(10,2) UNSIGNED NOT NULL,
  `dataIncasso_check` DATE GENERATED ALWAYS AS (IFNULL(dataIncasso, dataEmissione)) STORED,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_life_data` (`agenzia_id`, `codiceProdotto`, `polizza`, `dataContabile`, `dataIncasso_check`, `tipoPremioQuietanza`, `premio`),
  KEY `fk_agenzie` (`agenzia_id`),
  KEY `idx_dataIncasso` (`dataIncasso`),
  KEY `idx_policy` (`agenzia_id`, `codiceProdotto`, `polizza`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE `life_data`
    ADD CONSTRAINT `fk_life_data_agenzia_id` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`);