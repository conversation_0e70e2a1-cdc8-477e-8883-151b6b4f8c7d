
/* ======================= TABLES ======================================================= */

SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";

DROP VIEW IF EXISTS `vw_gestcontrib_contributi`;
DROP TABLE IF EXISTS `gestcontrib_contributi`;
CREATE TABLE IF NOT EXISTS `gestcontrib_contributi` (
	`id`				mediumint unsigned NOT NULL AUTO_INCREMENT,
	`anno`				year NOT NULL,
	`agenzia`			char(4) NOT NULL,
	`agVinc`			tinyint(1) unsigned NOT NULL default 0,
	`data`				date NOT NULL,
	`importo`			decimal(12,2) unsigned NOT NULL,
	`rateizzato`		enum('0','1') default '0',
	`codAccredito`		ENUM('210','505','521','502') NOT NULL,
	`ramo`				ENUM('D','V') NOT NULL,
	`tipo`				ENUM('PROD','ORG','PUBBL') NOT NULL,
	`budget`			ENUM('AREA','DIREZ') NOT NULL,
	`titolo`			varchar(50) NOT NULL,
	`descrizione`		text NULL default NULL,
	`allegato`			varchar(100) NULL default NULL,
	`status`			ENUM('DRAFT','PENDING','ACTIVE','DELETED') NOT NULL,
	`approvazione`		ENUM('PENDING','NO','OK','DIREZ') NULL default NULL,
	`createdAt`			datetime NOT NULL,
	`updatedAt`			timestamp NULL default NULL ON UPDATE CURRENT_TIMESTAMP,
	PRIMARY KEY (`id`),
	CONSTRAINT `fk_gestcontrib_agenzie` FOREIGN KEY (`agenzia`) REFERENCES `agenzie` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Contributi';


SET FOREIGN_KEY_CHECKS=1;

/* ======================= VIEWS ======================================================= */

CREATE VIEW `vw_gestcontrib_contributi` AS
SELECT
	c.*,
	ag.area AS agenziaArea,
	ag.localita AS agenziaLocalita,
	ag.nome AS agenziaNome
FROM
	`gestcontrib_contributi` c
	LEFT JOIN `agenzie` ag ON ( c.agenzia = ag.id )
;

/* ======================= TRIGGERS ======================================================= */

DROP TRIGGER IF EXISTS `triggerAI_gestcontrib_contributi`;
DROP TRIGGER IF EXISTS `triggerAU_gestcontrib_contributi`;
DROP TRIGGER IF EXISTS `triggerAD_gestcontrib_contributi`;

DELIMITER //
CREATE TRIGGER `triggerAI_gestcontrib_contributi` AFTER INSERT ON `gestcontrib_contributi`
	FOR EACH ROW BEGIN
		IF NEW.budget = 'AREA' THEN
			IF NEW.agVinc = 1 THEN
				CALL `gestcontrib_updateBudgetArea`(NEW.anno, (SELECT `area` FROM `agenzie` WHERE `id` = NEW.agenzia), 1);
			END IF;
			CALL `gestcontrib_updateBudgetArea`(NEW.anno, (SELECT `area` FROM `agenzie` WHERE `id` = NEW.agenzia), 0);
		END IF;
		IF NEW.budget = 'DIREZ' THEN
			IF NEW.agVinc = 1 THEN
				CALL `gestcontrib_updateBudgetDirezione`(NEW.anno, (SELECT `area` FROM `agenzie` WHERE `id` = NEW.agenzia), 1);
			END IF;
			CALL `gestcontrib_updateBudgetDirezione`(NEW.anno, (SELECT `area` FROM `agenzie` WHERE `id` = NEW.agenzia), 0);
		END IF;
	END; //
CREATE TRIGGER `triggerAU_gestcontrib_contributi` AFTER UPDATE ON `gestcontrib_contributi`
	FOR EACH ROW BEGIN
		IF NEW.budget = 'AREA' THEN
			IF NEW.agVinc = 1 THEN
				CALL `gestcontrib_updateBudgetArea`(NEW.anno, (SELECT `area` FROM `agenzie` WHERE `id` = NEW.agenzia), 1);
			END IF;
			CALL `gestcontrib_updateBudgetArea`(NEW.anno, (SELECT `area` FROM `agenzie` WHERE `id` = NEW.agenzia), 0);
		END IF;
		IF NEW.budget = 'DIREZ' THEN
			IF NEW.agVinc = 1 THEN
				CALL `gestcontrib_updateBudgetDirezione`(NEW.anno, (SELECT `area` FROM `agenzie` WHERE `id` = NEW.agenzia), 1);
			END IF;
			CALL `gestcontrib_updateBudgetDirezione`(NEW.anno, (SELECT `area` FROM `agenzie` WHERE `id` = NEW.agenzia), 0);
		END IF;
	END; //
CREATE TRIGGER `triggerAD_gestcontrib_contributi` AFTER DELETE ON `gestcontrib_contributi`
	FOR EACH ROW BEGIN
		IF OLD.budget = 'AREA' THEN
			IF OLD.agVinc = 1 THEN
				CALL `gestcontrib_updateBudgetArea`(OLD.anno, (SELECT `area` FROM `agenzie` WHERE `id` = OLD.agenzia), 1);
			END IF;
			CALL `gestcontrib_updateBudgetArea`(OLD.anno, (SELECT `area` FROM `agenzie` WHERE `id` = OLD.agenzia), 0);
		END IF;
		IF OLD.budget = 'DIREZ' THEN
			IF OLD.agVinc = 1 THEN
				CALL `gestcontrib_updateBudgetDirezione`(OLD.anno, (SELECT `area` FROM `agenzie` WHERE `id` = OLD.agenzia), 1);
			END IF;
			CALL `gestcontrib_updateBudgetDirezione`(OLD.anno, (SELECT `area` FROM `agenzie` WHERE `id` = OLD.agenzia), 0);
		END IF;
	END; //
DELIMITER ;

/* ======================= STORED PROCEDURES =========================================== */
