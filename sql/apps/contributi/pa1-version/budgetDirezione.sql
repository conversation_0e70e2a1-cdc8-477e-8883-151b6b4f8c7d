
/* ======================= TABLES ======================================================= */

SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";

DROP TABLE IF EXISTS `gestcontrib_budgetDirezione`;
CREATE TABLE IF NOT EXISTS `gestcontrib_budgetDirezione` (
	`id`					mediumint unsigned NOT NULL AUTO_INCREMENT,
	`area`					tinyint unsigned NOT NULL,
	`anno`					year NOT NULL,
	`agVinc`				tinyint(1) unsigned NOT NULL default 0,

	`spesePROD_DANN_GA`		decimal(12,2) unsigned NOT NULL,
	`spesePROD_DANN_NT`		decimal(12,2) unsigned NOT NULL,
	`spesePROD_VITA_GA`		decimal(12,2) unsigned NOT NULL,
	`spesePROD_VITA_NT`		decimal(12,2) unsigned NOT NULL,

	`speseORG_DANN_GA`		decimal(12,2) unsigned NOT NULL,
	`speseORG_DANN_NT`		decimal(12,2) unsigned NOT NULL,
	`speseORG_VITA_GA`		decimal(12,2) unsigned NOT NULL,
	`speseORG_VITA_NT`		decimal(12,2) unsigned NOT NULL,

	`spesePUBBL_DANN_GA`	decimal(12,2) unsigned NOT NULL,
	`spesePUBBL_DANN_NT`	decimal(12,2) unsigned NOT NULL,
	`spesePUBBL_VITA_GA`	decimal(12,2) unsigned NOT NULL,
	`spesePUBBL_VITA_NT`	decimal(12,2) unsigned NOT NULL,

	`updatedAt`				timestamp NULL default NULL ON UPDATE CURRENT_TIMESTAMP,
	PRIMARY KEY (`id`),
	UNIQUE KEY	`uk_budgetDirezione` (`area`,`anno`,`agVinc`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Budget di Direzione';


SET FOREIGN_KEY_CHECKS=1;

/* ======================= VIEWS ======================================================= */


/* ======================= STORED PROCEDURES =========================================== */

DROP PROCEDURE IF EXISTS `gestcontrib_updateBudgetDirezione`;
DELIMITER //
CREATE PROCEDURE `gestcontrib_updateBudgetDirezione` (
	IN p_anno	SMALLINT UNSIGNED,	-- should be YEAR, Warning: #1292 Incorrect datetime value
	IN p_area	tinyint,
	IN p_agVinc tinyint
)
BEGIN

	DECLARE v_spesePROD_DANN_GA		decimal(12,2);
	DECLARE v_spesePROD_DANN_NT		decimal(12,2);
	DECLARE v_spesePROD_VITA_GA		decimal(12,2);
	DECLARE v_spesePROD_VITA_NT		decimal(12,2);

	DECLARE v_speseORG_DANN_GA		decimal(12,2);
	DECLARE v_speseORG_DANN_NT		decimal(12,2);
	DECLARE v_speseORG_VITA_GA		decimal(12,2);
	DECLARE v_speseORG_VITA_NT		decimal(12,2);

	DECLARE v_spesePUBBL_DANN_GA	decimal(12,2);
	DECLARE v_spesePUBBL_DANN_NT	decimal(12,2);
	DECLARE v_spesePUBBL_VITA_GA	decimal(12,2);
	DECLARE v_spesePUBBL_VITA_NT	decimal(12,2);

	/* PROD */

	SELECT SUM(`importo`) INTO v_spesePROD_DANN_GA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'DIREZ' AND `tipo` = 'PROD' AND `ramo` = 'D' AND `agenzia` LIKE 'G%' AND `agVinc` >= p_agVinc;
	IF v_spesePROD_DANN_GA IS NULL THEN SET v_spesePROD_DANN_GA = 0; END IF;

	SELECT SUM(`importo`) INTO v_spesePROD_DANN_NT
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'DIREZ' AND `tipo` = 'PROD' AND `ramo` = 'D' AND `agenzia` LIKE 'N%' AND `agVinc` >= p_agVinc;
	IF v_spesePROD_DANN_NT IS NULL THEN SET v_spesePROD_DANN_NT = 0; END IF;

	SELECT SUM(`importo`) INTO v_spesePROD_VITA_GA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'DIREZ' AND `tipo` = 'PROD' AND `ramo` = 'V' AND `agenzia` LIKE 'G%' AND `agVinc` >= p_agVinc;
	IF v_spesePROD_VITA_GA IS NULL THEN SET v_spesePROD_VITA_GA = 0; END IF;

	SELECT SUM(`importo`) INTO v_spesePROD_VITA_NT
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'DIREZ' AND `tipo` = 'PROD' AND `ramo` = 'V' AND `agenzia` LIKE 'N%' AND `agVinc` >= p_agVinc;
	IF v_spesePROD_VITA_NT IS NULL THEN SET v_spesePROD_VITA_NT = 0; END IF;

	/* ORG */

	SELECT SUM(`importo`) INTO v_speseORG_DANN_GA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'DIREZ' AND `tipo` = 'ORG' AND `ramo` = 'D' AND `agenzia` LIKE 'G%' AND `agVinc` >= p_agVinc;
	IF v_speseORG_DANN_GA IS NULL THEN SET v_speseORG_DANN_GA = 0; END IF;

	SELECT SUM(`importo`) INTO v_speseORG_DANN_NT
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'DIREZ' AND `tipo` = 'ORG' AND `ramo` = 'D' AND `agenzia` LIKE 'N%' AND `agVinc` >= p_agVinc;
	IF v_speseORG_DANN_NT IS NULL THEN SET v_speseORG_DANN_NT = 0; END IF;

	SELECT SUM(`importo`) INTO v_speseORG_VITA_GA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'DIREZ' AND `tipo` = 'ORG' AND `ramo` = 'V' AND `agenzia` LIKE 'G%' AND `agVinc` >= p_agVinc;
	IF v_speseORG_VITA_GA IS NULL THEN SET v_speseORG_VITA_GA = 0; END IF;

	SELECT SUM(`importo`) INTO v_speseORG_VITA_NT
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'DIREZ' AND `tipo` = 'ORG' AND `ramo` = 'V' AND `agenzia` LIKE 'N%' AND `agVinc` >= p_agVinc;
	IF v_speseORG_VITA_NT IS NULL THEN SET v_speseORG_VITA_NT = 0; END IF;

	/* PUBBL */

	SELECT SUM(`importo`) INTO v_spesePUBBL_DANN_GA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'DIREZ' AND `tipo` = 'PUBBL' AND `ramo` = 'D' AND `agenzia` LIKE 'G%' AND `agVinc` >= p_agVinc;
	IF v_spesePUBBL_DANN_GA IS NULL THEN SET v_spesePUBBL_DANN_GA = 0; END IF;

	SELECT SUM(`importo`) INTO v_spesePUBBL_DANN_NT
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'DIREZ' AND `tipo` = 'PUBBL' AND `ramo` = 'D' AND `agenzia` LIKE 'N%' AND `agVinc` >= p_agVinc;
	IF v_spesePUBBL_DANN_NT IS NULL THEN SET v_spesePUBBL_DANN_NT = 0; END IF;

	SELECT SUM(`importo`) INTO v_spesePUBBL_VITA_GA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'DIREZ' AND `tipo` = 'PUBBL' AND `ramo` = 'V' AND `agenzia` LIKE 'G%' AND `agVinc` >= p_agVinc;
	IF v_spesePUBBL_VITA_GA IS NULL THEN SET v_spesePUBBL_VITA_GA = 0; END IF;

	SELECT SUM(`importo`) INTO v_spesePUBBL_VITA_NT
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'DIREZ' AND `tipo` = 'PUBBL' AND `ramo` = 'V' AND `agenzia` LIKE 'N%' AND `agVinc` >= p_agVinc;
	IF v_spesePUBBL_VITA_NT IS NULL THEN SET v_spesePUBBL_VITA_NT = 0; END IF;


	UPDATE `gestcontrib_budgetDirezione` SET
		spesePROD_DANN_GA = v_spesePROD_DANN_GA, spesePROD_DANN_NT = v_spesePROD_DANN_NT, spesePROD_VITA_GA = v_spesePROD_VITA_GA, spesePROD_VITA_NT = v_spesePROD_VITA_NT,
		speseORG_DANN_GA = v_speseORG_DANN_GA, speseORG_DANN_NT = v_speseORG_DANN_NT, speseORG_VITA_GA = v_speseORG_VITA_GA, speseORG_VITA_NT = v_speseORG_VITA_NT,
		spesePUBBL_DANN_GA = v_spesePUBBL_DANN_GA, spesePUBBL_DANN_NT = v_spesePUBBL_DANN_NT, spesePUBBL_VITA_GA = v_spesePUBBL_VITA_GA, spesePUBBL_VITA_NT = v_spesePUBBL_VITA_NT
		WHERE `area` = p_area AND `anno` = p_anno AND `agVinc` = p_agVinc;

END; //
DELIMITER ;
