
/* ======================= TABLES ======================================================= */

SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";

DROP TABLE IF EXISTS `gestcontrib_budgetArea`;
CREATE TABLE IF NOT EXISTS `gestcontrib_budgetArea` (
	`id`					mediumint unsigned NOT NULL AUTO_INCREMENT,
	`area`					tinyint unsigned NOT NULL,
	`anno`					year NOT NULL,
	`agVinc`				tinyint(1) unsigned NOT NULL default 0,
	`budgetPROD`			decimal(12,2) unsigned NOT NULL,
	`budgetORG`				decimal(12,2) unsigned NOT NULL,
	`budgetPUBBL`			decimal(12,2) unsigned NOT NULL,

	`disponibilePROD`		decimal(12,2) unsigned NOT NULL,
	`disponibileORG`		decimal(12,2) unsigned NOT NULL,
	`disponibilePUBBL`		decimal(12,2) unsigned NOT NULL,

	`erogatiPROD_VITA`		decimal(12,2) unsigned NOT NULL,
	`erogatiORG_VITA`		decimal(12,2) unsigned NOT NULL,
	`erogatiPUBBL_VITA`		decimal(12,2) unsigned NOT NULL,
	`erogatiPROD_DANN`		decimal(12,2) unsigned NOT NULL,
	`erogatiORG_DANN`		decimal(12,2) unsigned NOT NULL,
	`erogatiPUBBL_DANN`		decimal(12,2) unsigned NOT NULL,

	`impegniPROD_VITA`		decimal(12,2) unsigned NOT NULL,
	`impegniORG_VITA`		decimal(12,2) unsigned NOT NULL,
	`impegniPUBBL_VITA`		decimal(12,2) unsigned NOT NULL,
	`impegniPROD_DANN`		decimal(12,2) unsigned NOT NULL,
	`impegniORG_DANN`		decimal(12,2) unsigned NOT NULL,
	`impegniPUBBL_DANN`		decimal(12,2) unsigned NOT NULL,

	`aggregatoPROD_DANN_GA`		decimal(12,2) unsigned NOT NULL,
	`aggregatoPROD_DANN_NT`		decimal(12,2) unsigned NOT NULL,
	`aggregatoPROD_VITA_GA`		decimal(12,2) unsigned NOT NULL,
	`aggregatoPROD_VITA_NT`		decimal(12,2) unsigned NOT NULL,
	`aggregatoORG_DANN_GA`		decimal(12,2) unsigned NOT NULL,
	`aggregatoORG_DANN_NT`		decimal(12,2) unsigned NOT NULL,
	`aggregatoORG_VITA_GA`		decimal(12,2) unsigned NOT NULL,
	`aggregatoORG_VITA_NT`		decimal(12,2) unsigned NOT NULL,
	`aggregatoPUBBL_DANN_GA`	decimal(12,2) unsigned NOT NULL,
	`aggregatoPUBBL_DANN_NT`	decimal(12,2) unsigned NOT NULL,
	`aggregatoPUBBL_VITA_GA`	decimal(12,2) unsigned NOT NULL,
	`aggregatoPUBBL_VITA_NT`	decimal(12,2) unsigned NOT NULL,

	`updatedAt`				timestamp NULL default NULL ON UPDATE CURRENT_TIMESTAMP,
	PRIMARY KEY (`id`),
	UNIQUE KEY	`uk_budgetArea` (`area`,`anno`,`agVinc`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Budget di Area';

SET FOREIGN_KEY_CHECKS=1;

/* ======================= VIEWS ======================================================= */


/* ======================= STORED PROCEDURES =========================================== */

DROP PROCEDURE IF EXISTS `gestcontrib_updateBudgetArea`;
DELIMITER //
CREATE PROCEDURE `gestcontrib_updateBudgetArea` (
	IN p_anno	SMALLINT UNSIGNED,	-- should be YEAR, Warning: #1292 Incorrect datetime value
	IN p_area	tinyint,
	IN p_agVinc tinyint
)
BEGIN

	DECLARE v_budgetPROD				decimal(12,2);
	DECLARE v_budgetORG					decimal(12,2);
	DECLARE v_budgetPUBBL				decimal(12,2);

	DECLARE v_erogatiPROD_VITA			decimal(12,2);
	DECLARE v_erogatiORG_VITA			decimal(12,2);
	DECLARE v_erogatiPUBBL_VITA			decimal(12,2);
	DECLARE v_erogatiPROD_DANN			decimal(12,2);
	DECLARE v_erogatiORG_DANN			decimal(12,2);
	DECLARE v_erogatiPUBBL_DANN			decimal(12,2);

	DECLARE v_impegniPROD_VITA			decimal(12,2);
	DECLARE v_impegniORG_VITA			decimal(12,2);
	DECLARE v_impegniPUBBL_VITA			decimal(12,2);
	DECLARE v_impegniPROD_DANN			decimal(12,2);
	DECLARE v_impegniORG_DANN			decimal(12,2);
	DECLARE v_impegniPUBBL_DANN			decimal(12,2);

	DECLARE v_aggregatoPROD_DANN_GA		decimal(12,2);
	DECLARE v_aggregatoPROD_DANN_NT		decimal(12,2);
	DECLARE v_aggregatoPROD_VITA_GA		decimal(12,2);
	DECLARE v_aggregatoPROD_VITA_NT		decimal(12,2);
	DECLARE v_aggregatoORG_DANN_GA		decimal(12,2);
	DECLARE v_aggregatoORG_DANN_NT		decimal(12,2);
	DECLARE v_aggregatoORG_VITA_GA		decimal(12,2);
	DECLARE v_aggregatoORG_VITA_NT		decimal(12,2);
	DECLARE v_aggregatoPUBBL_DANN_GA	decimal(12,2);
	DECLARE v_aggregatoPUBBL_DANN_NT	decimal(12,2);
	DECLARE v_aggregatoPUBBL_VITA_GA	decimal(12,2);
	DECLARE v_aggregatoPUBBL_VITA_NT	decimal(12,2);


	SELECT 	`budgetPROD`, `budgetORG`, `budgetPUBBL` INTO v_budgetPROD, v_budgetORG, v_budgetPUBBL
		FROM `gestcontrib_budgetArea`
		WHERE `area` = p_area AND `anno` = p_anno AND `agVinc` = p_agVinc;

	/* EROGATI */

	SELECT SUM(`importo`) INTO v_erogatiPROD_VITA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'AREA' AND `tipo` = 'PROD' AND `ramo` = 'V' AND `agVinc` >= p_agVinc;
	IF v_erogatiPROD_VITA IS NULL THEN SET v_erogatiPROD_VITA = 0; END IF;

	SELECT SUM(`importo`) INTO v_erogatiORG_VITA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'AREA' AND `tipo` = 'ORG' AND `ramo` = 'V' AND `agVinc` >= p_agVinc;
	IF v_erogatiORG_VITA IS NULL THEN SET v_erogatiORG_VITA = 0; END IF;

	SELECT SUM(`importo`) INTO v_erogatiPUBBL_VITA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'AREA' AND `tipo` = 'PUBBL' AND `ramo` = 'V' AND `agVinc` >= p_agVinc;
	IF v_erogatiPUBBL_VITA IS NULL THEN SET v_erogatiPUBBL_VITA = 0; END IF;

	SELECT SUM(`importo`) INTO v_erogatiPROD_DANN
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'AREA' AND `tipo` = 'PROD' AND `ramo` = 'D' AND `agVinc` >= p_agVinc;
	IF v_erogatiPROD_DANN IS NULL THEN SET v_erogatiPROD_DANN = 0; END IF;

	SELECT SUM(`importo`) INTO v_erogatiORG_DANN
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'AREA' AND `tipo` = 'ORG' AND `ramo` = 'D' AND `agVinc` >= p_agVinc;
	IF v_erogatiORG_DANN IS NULL THEN SET v_erogatiORG_DANN = 0; END IF;

	SELECT SUM(`importo`) INTO v_erogatiPUBBL_DANN
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'AREA' AND `tipo` = 'PUBBL' AND `ramo` = 'D' AND `agVinc` >= p_agVinc;
	IF v_erogatiPUBBL_DANN IS NULL THEN SET v_erogatiPUBBL_DANN = 0; END IF;

	/* IMPEGNATI */

	SELECT SUM(`importo`) INTO v_impegniPROD_VITA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'DRAFT' AND `budget` = 'AREA' AND `tipo` = 'PROD' AND `ramo` = 'V' AND `agVinc` >= p_agVinc;
	IF v_impegniPROD_VITA IS NULL THEN SET v_impegniPROD_VITA = 0; END IF;

	SELECT SUM(`importo`) INTO v_impegniORG_VITA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'DRAFT' AND `budget` = 'AREA' AND `tipo` = 'ORG' AND `ramo` = 'V' AND `agVinc` >= p_agVinc;
	IF v_impegniORG_VITA IS NULL THEN SET v_impegniORG_VITA = 0; END IF;

	SELECT SUM(`importo`) INTO v_impegniPUBBL_VITA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'DRAFT' AND `budget` = 'AREA' AND `tipo` = 'PUBBL' AND `ramo` = 'V' AND `agVinc` >= p_agVinc;
	IF v_impegniPUBBL_VITA IS NULL THEN SET v_impegniPUBBL_VITA = 0; END IF;

	SELECT SUM(`importo`) INTO v_impegniPROD_DANN
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'DRAFT' AND `budget` = 'AREA' AND `tipo` = 'PROD' AND `ramo` = 'D' AND `agVinc` >= p_agVinc;
	IF v_impegniPROD_DANN IS NULL THEN SET v_impegniPROD_DANN = 0; END IF;

	SELECT SUM(`importo`) INTO v_impegniORG_DANN
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'DRAFT' AND `budget` = 'AREA' AND `tipo` = 'ORG' AND `ramo` = 'D' AND `agVinc` >= p_agVinc;
	IF v_impegniORG_DANN IS NULL THEN SET v_impegniORG_DANN = 0; END IF;

	SELECT SUM(`importo`) INTO v_impegniPUBBL_DANN
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'DRAFT' AND `budget` = 'AREA' AND `tipo` = 'PUBBL' AND `ramo` = 'D' AND `agVinc` >= p_agVinc;
	IF v_impegniPUBBL_DANN IS NULL THEN SET v_impegniPUBBL_DANN = 0; END IF;

	/* AGGREGATI */

	SELECT SUM(`importo`) INTO v_aggregatoPROD_DANN_GA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND (`status` = 'ACTIVE' OR `status` = 'DRAFT') AND `budget` = 'AREA' AND `tipo` = 'PROD' AND `ramo` = 'D' AND `agenzia` LIKE 'G%' AND `agVinc` >= p_agVinc;
	IF v_aggregatoPROD_DANN_GA IS NULL THEN SET v_aggregatoPROD_DANN_GA = 0; END IF;

	SELECT SUM(`importo`) INTO v_aggregatoPROD_DANN_NT
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND (`status` = 'ACTIVE' OR `status` = 'DRAFT') AND `budget` = 'AREA' AND `tipo` = 'PROD' AND `ramo` = 'D' AND `agenzia` LIKE 'N%' AND `agVinc` >= p_agVinc;
	IF v_aggregatoPROD_DANN_NT IS NULL THEN SET v_aggregatoPROD_DANN_NT = 0; END IF;

	SELECT SUM(`importo`) INTO v_aggregatoPROD_VITA_GA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND (`status` = 'ACTIVE' OR `status` = 'DRAFT') AND `budget` = 'AREA' AND `tipo` = 'PROD' AND `ramo` = 'V' AND `agenzia` LIKE 'G%' AND `agVinc` >= p_agVinc;
	IF v_aggregatoPROD_VITA_GA IS NULL THEN SET v_aggregatoPROD_VITA_GA = 0; END IF;

	SELECT SUM(`importo`) INTO v_aggregatoPROD_VITA_NT
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND (`status` = 'ACTIVE' OR `status` = 'DRAFT') AND `budget` = 'AREA' AND `tipo` = 'PROD' AND `ramo` = 'V' AND `agenzia` LIKE 'N%' AND `agVinc` >= p_agVinc;
	IF v_aggregatoPROD_VITA_NT IS NULL THEN SET v_aggregatoPROD_VITA_NT = 0; END IF;

	SELECT SUM(`importo`) INTO v_aggregatoORG_DANN_GA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND (`status` = 'ACTIVE' OR `status` = 'DRAFT') AND `budget` = 'AREA' AND `tipo` = 'ORG' AND `ramo` = 'D' AND `agenzia` LIKE 'G%' AND `agVinc` >= p_agVinc;
	IF v_aggregatoORG_DANN_GA IS NULL THEN SET v_aggregatoORG_DANN_GA = 0; END IF;

	SELECT SUM(`importo`) INTO v_aggregatoORG_DANN_NT
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND (`status` = 'ACTIVE' OR `status` = 'DRAFT') AND `budget` = 'AREA' AND `tipo` = 'ORG' AND `ramo` = 'D' AND `agenzia` LIKE 'N%' AND `agVinc` >= p_agVinc;
	IF v_aggregatoORG_DANN_NT IS NULL THEN SET v_aggregatoORG_DANN_NT = 0; END IF;

	SELECT SUM(`importo`) INTO v_aggregatoORG_VITA_GA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND (`status` = 'ACTIVE' OR `status` = 'DRAFT') AND `budget` = 'AREA' AND `tipo` = 'ORG' AND `ramo` = 'V' AND `agenzia` LIKE 'G%' AND `agVinc` >= p_agVinc;
	IF v_aggregatoORG_VITA_GA IS NULL THEN SET v_aggregatoORG_VITA_GA = 0; END IF;

	SELECT SUM(`importo`) INTO v_aggregatoORG_VITA_NT
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND (`status` = 'ACTIVE' OR `status` = 'DRAFT') AND `budget` = 'AREA' AND `tipo` = 'ORG' AND `ramo` = 'V' AND `agenzia` LIKE 'N%' AND `agVinc` >= p_agVinc;
	IF v_aggregatoORG_VITA_NT IS NULL THEN SET v_aggregatoORG_VITA_NT = 0; END IF;

	SELECT SUM(`importo`) INTO v_aggregatoPUBBL_DANN_GA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND (`status` = 'ACTIVE' OR `status` = 'DRAFT') AND `budget` = 'AREA' AND `tipo` = 'PUBBL' AND `ramo` = 'D' AND `agenzia` LIKE 'G%' AND `agVinc` >= p_agVinc;
	IF v_aggregatoPUBBL_DANN_GA IS NULL THEN SET v_aggregatoPUBBL_DANN_GA = 0; END IF;

	SELECT SUM(`importo`) INTO v_aggregatoPUBBL_DANN_NT
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND (`status` = 'ACTIVE' OR `status` = 'DRAFT') AND `budget` = 'AREA' AND `tipo` = 'PUBBL' AND `ramo` = 'D' AND `agenzia` LIKE 'N%' AND `agVinc` >= p_agVinc;
	IF v_aggregatoPUBBL_DANN_NT IS NULL THEN SET v_aggregatoPUBBL_DANN_NT = 0; END IF;

	SELECT SUM(`importo`) INTO v_aggregatoPUBBL_VITA_GA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND (`status` = 'ACTIVE' OR `status` = 'DRAFT') AND `budget` = 'AREA' AND `tipo` = 'PUBBL' AND `ramo` = 'V' AND `agenzia` LIKE 'G%' AND `agVinc` >= p_agVinc;
	IF v_aggregatoPUBBL_VITA_GA IS NULL THEN SET v_aggregatoPUBBL_VITA_GA = 0; END IF;

	SELECT SUM(`importo`) INTO v_aggregatoPUBBL_VITA_NT
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND (`status` = 'ACTIVE' OR `status` = 'DRAFT') AND `budget` = 'AREA' AND `tipo` = 'PUBBL' AND `ramo` = 'V' AND `agenzia` LIKE 'N%' AND `agVinc` >= p_agVinc;
	IF v_aggregatoPUBBL_VITA_NT IS NULL THEN SET v_aggregatoPUBBL_VITA_NT = 0; END IF;

	UPDATE `gestcontrib_budgetArea` SET
		erogatiPROD_VITA = v_erogatiPROD_VITA, erogatiORG_VITA = v_erogatiORG_VITA, erogatiPUBBL_VITA = v_erogatiPUBBL_VITA,
		erogatiPROD_DANN = v_erogatiPROD_DANN, erogatiORG_DANN = v_erogatiORG_DANN, erogatiPUBBL_DANN = v_erogatiPUBBL_DANN,
		impegniPROD_VITA = v_impegniPROD_VITA, impegniORG_VITA = v_impegniORG_VITA, impegniPUBBL_VITA = v_impegniPUBBL_VITA,
		impegniPROD_DANN = v_impegniPROD_DANN, impegniORG_DANN = v_impegniORG_DANN, impegniPUBBL_DANN = v_impegniPUBBL_DANN,

		disponibilePROD	 = budgetPROD	- v_erogatiPROD_VITA	- v_erogatiPROD_DANN	- v_impegniPROD_VITA	- v_impegniPROD_DANN,
		disponibileORG	 = budgetORG	- v_erogatiORG_VITA		- v_erogatiORG_DANN		- v_impegniORG_VITA		- v_impegniORG_DANN,
		disponibilePUBBL = budgetPUBBL 	- v_erogatiPUBBL_VITA	- v_erogatiPUBBL_DANN	- v_impegniPUBBL_VITA	- v_impegniPUBBL_DANN,

		aggregatoPROD_DANN_GA	= v_aggregatoPROD_DANN_GA,	aggregatoPROD_DANN_NT	= v_aggregatoPROD_DANN_NT,
		aggregatoPROD_VITA_GA	= v_aggregatoPROD_VITA_GA,	aggregatoPROD_VITA_NT	= v_aggregatoPROD_VITA_NT,
		aggregatoORG_DANN_GA	= v_aggregatoORG_DANN_GA,	aggregatoORG_DANN_NT	= v_aggregatoORG_DANN_NT,
		aggregatoORG_VITA_GA	= v_aggregatoORG_VITA_GA,	aggregatoORG_VITA_NT	= v_aggregatoORG_VITA_NT,
		aggregatoPUBBL_DANN_GA	= v_aggregatoPUBBL_DANN_GA,	aggregatoPUBBL_DANN_NT	= v_aggregatoPUBBL_DANN_NT,
		aggregatoPUBBL_VITA_GA	= v_aggregatoPUBBL_VITA_GA,	aggregatoPUBBL_VITA_NT	= v_aggregatoPUBBL_VITA_NT

		WHERE `area` = p_area AND `anno` = p_anno AND `agVinc` = p_agVinc;

END; //
DELIMITER ;
