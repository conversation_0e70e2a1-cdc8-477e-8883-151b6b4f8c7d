/* ======================= SETUP ======================================================== */

INSERT IGNORE INTO gare (id, nome, url, anno, tutelaPersona) VALUES (302, 'Gara Risparmio e Protezione persona', 'GaraRisparmioProtezionePersona', 2023, 1);

/* ======================= TABLES ======================================================= */

SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";

DROP TABLE IF EXISTS gare_302;
CREATE TABLE IF NOT EXISTS gare_302 (
	agenzia_id					CHAR(4) NOT NULL,
	gruppo						CHAR(2) NOT NULL,
	obiettivoPezziRisparmio		MEDIUMINT UNSIGNED NOT NULL DEFAULT 0,
	obiettivoPezziProtezione	MEDIUMINT UNSIGNED NOT NULL DEFAULT 0,
	obiettivoPremi				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	pezziRisparmio				MEDIUMINT UNSIGNED NOT NULL DEFAULT 0,
	pezziProtezione				MEDIUMINT UNSIGNED NOT NULL DEFAULT 0,
	premi						DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	posizione					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	PRIMARY KEY (agenzia_id),
	CONSTRAINT fk_gare_302 FOREIGN KEY (agenzia_id) REFERENCES agenzie (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

SET FOREIGN_KEY_CHECKS=1;

/* ======================= VIEWS ======================================================= */

DROP VIEW IF EXISTS vw_gare_302;
CREATE VIEW vw_gare_302 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivoPezziRisparmio,
	st.obiettivoPezziProtezione,
	st.obiettivoPremi,
	st.pezziRisparmio,
	st.pezziProtezione,
	st.premi,
	st.posizione
FROM
	gare_302 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

/* ======================= STORED PROCEDURES =========================================== */

DROP PROCEDURE IF EXISTS gare_302_insert_obiettivi;
DELIMITER //
CREATE PROCEDURE gare_302_insert_obiettivi (
	IN p_agenzia_id			CHAR(4),
	IN p_gruppo				VARCHAR(2),
	IN p_obiettivoPezzi1	MEDIUMINT UNSIGNED,
	IN p_obiettivoPezzi2	MEDIUMINT UNSIGNED,
	IN p_obiettivoPremi1	DECIMAL(12,2) UNSIGNED,
	IN p_obiettivoPremi2	DECIMAL(12,2) UNSIGNED	-- NOT USED
)
BEGIN
	INSERT INTO gare_302 (
		agenzia_id,
		gruppo, obiettivoPezziRisparmio, obiettivoPezziProtezione, obiettivoPremi
	) VALUES (
		p_agenzia_id,
		p_gruppo, p_obiettivoPezzi1, p_obiettivoPezzi2, p_obiettivoPremi1
	) ON DUPLICATE KEY UPDATE
		gruppo = p_gruppo, obiettivoPezziRisparmio = p_obiettivoPezzi1, obiettivoPezziProtezione = p_obiettivoPezzi2, obiettivoPremi = p_obiettivoPremi1
	;
END; //
DELIMITER ;

DROP PROCEDURE IF EXISTS gare_302_insert_status;
DELIMITER //
CREATE PROCEDURE gare_302_insert_status (
	IN p_dataEstrazione		DATE,
	IN p_agenzia_id			CHAR(4),
	IN p_pezzi1				MEDIUMINT UNSIGNED,
	IN p_pezzi2				MEDIUMINT UNSIGNED,
	IN p_premi1				DECIMAL(12,2) UNSIGNED,
	IN p_premi2				DECIMAL(12,2) UNSIGNED,	-- NOT USED
	IN p_punti				MEDIUMINT UNSIGNED		-- NOT USED
)
BEGIN
	SET @gara_302_dataUpdate = p_dataEstrazione;

	UPDATE gare_302 SET
		pezziRisparmio = p_pezzi1, pezziProtezione = p_pezzi2, premi = p_premi1
		WHERE agenzia_id = p_agenzia_id;

END; //
DELIMITER ;


DROP PROCEDURE IF EXISTS gare_302_set_classifica;
DELIMITER //
CREATE PROCEDURE gare_302_set_classifica (
	IN p_gruppo					CHAR(2)
)
BEGIN
	DECLARE done 						TINYINT UNSIGNED DEFAULT 0;
	DECLARE v_agenzia_id				CHAR(4);
	DECLARE v_posizione					SMALLINT UNSIGNED;
	-- CURSORS
	DECLARE cur_agenzia CURSOR FOR
		SELECT agenzia_id FROM gare_302
		WHERE gruppo = p_gruppo AND premi > 0 AND pezziRisparmio >= obiettivoPezziRisparmio AND pezziProtezione >= obiettivoPezziProtezione AND premi >= obiettivoPremi
		ORDER BY premi DESC, pezziRisparmio DESC, pezziProtezione DESC;
	DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
	-- PROCEDURE
	UPDATE gare_302 SET posizione = 0 WHERE gruppo = p_gruppo;
	SET done = 0;
	SET v_posizione = 0;
	OPEN cur_agenzia;
	FETCH cur_agenzia INTO v_agenzia_id;
	WHILE NOT done DO
		SET v_posizione = v_posizione + 1;
		UPDATE gare_302 SET posizione = v_posizione
			WHERE agenzia_id = v_agenzia_id
		;
        FETCH cur_agenzia INTO v_agenzia_id;
    END WHILE;
    CLOSE cur_agenzia;

END; //
DELIMITER ;


DROP PROCEDURE IF EXISTS gare_302_set_classifica_area;
DELIMITER //
CREATE PROCEDURE gare_302_set_classifica_area (
	IN p_area					TINYINT UNSIGNED
)
BEGIN
	DECLARE done 						TINYINT UNSIGNED DEFAULT 0;
	DECLARE v_agenzia_id				CHAR(4);
	DECLARE v_posizione					SMALLINT UNSIGNED;
	-- CURSORS
	DECLARE cur_agenzia CURSOR FOR
		SELECT g.agenzia_id FROM gare_302 g LEFT JOIN agenzie agz ON g.agenzia_id = agz.id
		WHERE agz.area = p_area AND g.gruppo = 'GA' AND g.premi > 0 AND g.pezziRisparmio >= g.obiettivoPezziRisparmio AND g.pezziProtezione >= g.obiettivoPezziProtezione AND g.premi >= g.obiettivoPremi
		ORDER BY g.premi DESC, g.pezziRisparmio DESC, g.pezziProtezione DESC;
	DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
	-- PROCEDURE
	SET done = 0;
	SET v_posizione = 0;
	OPEN cur_agenzia;
	FETCH cur_agenzia INTO v_agenzia_id;
	WHILE NOT done DO
		SET v_posizione = v_posizione + 1;
		UPDATE gare_302 SET posizione = v_posizione
			WHERE agenzia_id = v_agenzia_id
		;
        FETCH cur_agenzia INTO v_agenzia_id;
    END WHILE;
    CLOSE cur_agenzia;
END; //
DELIMITER ;
