/* ======================= SETUP ======================================================== */

INSERT IGNORE INTO gare (id, nome, url, anno, tutelaAuto, tutelaBeni, tutelaPersona) VALUES (253, 'Gara Globale', 'GaraGlobale', 2021, 1, 1, 1);

/* ======================= TABLES ======================================================= */

SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";

DROP TABLE IF EXISTS gare_253;
CREATE TABLE IF NOT EXISTS gare_253 (
	agenzia_id					CHAR(4) NOT NULL,
	gruppo						CHAR(2) NOT NULL,
	obiettivoRamiElem			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	obiettivoVitaIndiv			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	punti						MEDIUMINT UNSIGNED NOT NULL DEFAULT 0,
	posizione					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	PRIMARY KEY (agenzia_id),
	CONSTRAINT fk_gare_253 FOREIGN KEY (agenzia_id) REFERENCES agenzie (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS gare_253_poll;
CREATE TABLE IF NOT EXISTS gare_253_poll (
	agenzia_id				CHAR(4) NOT NULL,
	voto					ENUM('ALASKA','CILE','GUATEMALAMIAMI','MALESIA','MADAGASCAR','SRILANKA') NULL DEFAULT NULL,
	dataVoto				DATETIME NULL DEFAULT NULL,
	PRIMARY KEY (agenzia_id),
	CONSTRAINT fk_iniz253_poll FOREIGN KEY (agenzia_id) REFERENCES agenzie (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

SET FOREIGN_KEY_CHECKS=1;

/* ======================= VIEWS ======================================================= */

DROP VIEW IF EXISTS vw_gare_253;
CREATE VIEW vw_gare_253 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivoRamiElem,
	st.obiettivoVitaIndiv,
	st.punti,
	st.posizione
FROM
	gare_253 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_gare_253_poll;
CREATE VIEW vw_gare_253_poll AS
	SELECT voto, count(*) as n
	FROM gare_253_poll
	GROUP BY voto
;

/* ======================= STORED PROCEDURES =========================================== */

DROP PROCEDURE IF EXISTS gare_253_insert_obiettivi;
DELIMITER //
CREATE PROCEDURE gare_253_insert_obiettivi (
	IN p_agenzia_id			CHAR(4),
	IN p_gruppo				VARCHAR(2),
	IN p_obiettivoPezzi1	MEDIUMINT UNSIGNED,		-- NOT USED
	IN p_obiettivoPezzi2	MEDIUMINT UNSIGNED,		-- NOT USED
	IN p_obiettivoPremi1	DECIMAL(12,2) UNSIGNED,
	IN p_obiettivoPremi2	DECIMAL(12,2) UNSIGNED
)
BEGIN
	INSERT INTO gare_253 (
		agenzia_id,
		gruppo, obiettivoRamiElem, obiettivoVitaIndiv
	) VALUES (
		p_agenzia_id,
		p_gruppo, p_obiettivoPremi1, p_obiettivoPremi2
	) ON DUPLICATE KEY UPDATE
		gruppo = p_gruppo, obiettivoRamiElem = p_obiettivoPremi1, obiettivoVitaIndiv = p_obiettivoPremi2
	;
END; //
DELIMITER ;

DROP PROCEDURE IF EXISTS gare_253_insert_status;
DELIMITER //
CREATE PROCEDURE gare_253_insert_status (
	IN p_dataEstrazione		DATE,
	IN p_agenzia_id			CHAR(4),
	IN p_pezzi1				MEDIUMINT UNSIGNED,		-- NOT USED
	IN p_pezzi2				MEDIUMINT UNSIGNED,		-- NOT USED
	IN p_premi1				DECIMAL(12,2) UNSIGNED,	-- NOT USED
	IN p_premi2				DECIMAL(12,2) UNSIGNED,	-- NOT USED
	IN p_punti				MEDIUMINT UNSIGNED
)
BEGIN
	SET @gara_253_dataUpdate = p_dataEstrazione;

	UPDATE gare_253 SET
		punti = p_punti
		WHERE agenzia_id = p_agenzia_id;

END; //
DELIMITER ;


DROP PROCEDURE IF EXISTS gare_253_set_classifica;
DELIMITER //
CREATE PROCEDURE gare_253_set_classifica (
	IN p_gruppo					CHAR(2)
)
BEGIN
	DECLARE done 						TINYINT UNSIGNED DEFAULT 0;
	DECLARE v_agenzia_id				CHAR(4);
	DECLARE v_posizione					SMALLINT UNSIGNED;
	-- CURSORS
	DECLARE cur_agenzia CURSOR FOR
		SELECT agenzia_id FROM gare_253
		WHERE gruppo = p_gruppo AND punti > 0
		ORDER BY punti DESC;
	DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
	-- PROCEDURE
	UPDATE gare_253 SET posizione = 0 WHERE gruppo = p_gruppo AND punti = 0;
	SET done = 0;
	SET v_posizione = 0;
	OPEN cur_agenzia;
	FETCH cur_agenzia INTO v_agenzia_id;
	WHILE NOT done DO
		SET v_posizione = v_posizione + 1;
		UPDATE gare_253 SET posizione = v_posizione
			WHERE agenzia_id = v_agenzia_id
		;
        FETCH cur_agenzia INTO v_agenzia_id;
    END WHILE;
    CLOSE cur_agenzia;

END; //
DELIMITER ;
