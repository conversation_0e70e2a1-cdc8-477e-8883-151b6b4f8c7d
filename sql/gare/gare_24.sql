/* ======================= TABLES ======================================================= */

SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";

DROP TABLE IF EXISTS `gare_24`;
CREATE TABLE IF NOT EXISTS `gare_24` (
	`agenzia_id`					char(4) NOT NULL,
	`gruppo`						CHAR(2) NOT NULL,
	`punti`							MEDIUMINT unsigned NOT NULL default 0,
	`posizione`						SMALLINT unsigned NOT NULL default 0,
	PRIMARY KEY (`agenzia_id`),
	CONSTRAINT `fk_iniz24` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

SET FOREIGN_KEY_CHECKS=1;

/* ======================= VIEWS ======================================================= */

DROP VIEW IF EXISTS `vw_gare_24`;
CREATE VIEW `vw_gare_24` AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.punti,
	st.posizione
FROM
	`gare_24` st
	LEFT JOIN `agenzie` ag ON ( st.agenzia_id = ag.id )
;

/* ======================= STORED PROCEDURES =========================================== */

DROP PROCEDURE IF EXISTS `gare_24_insert_obiettivi`;
DELIMITER //
CREATE PROCEDURE `gare_24_insert_obiettivi` (
	IN p_iniziativa_id		TINYINT, /* NOT USED */
	IN p_idCompagnia		CHAR(1),
	IN p_idAgenzia			CHAR(3),
	IN p_gruppo				VARCHAR(2),
	IN p_NOTUSED1			MEDIUMINT,
	IN p_NOTUSED2		DECIMAL(12,2)
)
BEGIN
	DECLARE p_agenzia_id CHAR(4);
	SET p_agenzia_id = CONCAT(p_idCompagnia, p_idAgenzia);

	INSERT INTO `gare_24` (
		agenzia_id,
		gruppo
	) VALUES (
		p_agenzia_id,
		p_gruppo
	) ON DUPLICATE KEY UPDATE
		gruppo = p_gruppo
	;
END; //
DELIMITER ;

DROP PROCEDURE IF EXISTS `gare_24_insert_status`;
DELIMITER //
CREATE PROCEDURE `gare_24_insert_status` (
	IN p_iniziativa_id		TINYINT, /* NOT USED */
	IN p_dataEstrazione		DATE,
	IN p_idCompagnia		CHAR(1),
	IN p_idAgenzia			CHAR(3),
	IN p_punti				MEDIUMINT,
	IN p_NOTUSED1			DECIMAL(12,2)
)
BEGIN
	DECLARE p_agenzia_id CHAR(4);
	SET p_agenzia_id = CONCAT(p_idCompagnia, p_idAgenzia);

	SET @gara_24_dataUpdate = p_dataEstrazione;

	UPDATE `gare_24` SET
		punti = p_punti
		WHERE agenzia_id = p_agenzia_id;


END; //
DELIMITER ;


DROP PROCEDURE IF EXISTS `gare_24_set_classifica`;
DELIMITER //
CREATE PROCEDURE `gare_24_set_classifica` (
	IN p_gruppo					CHAR(2)
)
BEGIN
	DECLARE done 						INT default 0;
	DECLARE v_agenzia_id					CHAR(4);
	DECLARE v_posizione					SMALLINT;
	/* CURSORS  */
	DECLARE cur_agenzia CURSOR FOR
		SELECT agenzia_id FROM `gare_24`
		WHERE gruppo = p_gruppo AND punti > 0
		ORDER BY punti DESC;
	DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
	/* PROCEDURE */
	SET done = 0;
	SET v_posizione = 0;
	OPEN cur_agenzia;
	FETCH cur_agenzia INTO v_agenzia_id;
	WHILE NOT done DO
		/* set posizione */
		SET v_posizione = v_posizione + 1;
		/* update tables */
		UPDATE `gare_24` SET posizione = v_posizione
			WHERE agenzia_id = v_agenzia_id
		;
        FETCH cur_agenzia INTO v_agenzia_id;
    END WHILE;
    CLOSE cur_agenzia;

END; //
DELIMITER ;

