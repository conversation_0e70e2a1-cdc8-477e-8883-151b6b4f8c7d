/* ======================= SETUP ======================================================== */

-- INSERT IGNORE INTO iniz (id, nome, url, statusAMM, statusAGT, statusINT) VALUES ('79', 'Gara Speciale Privati 2014', 'GaraSpecialePrivati2014', 'OFF', 'OFF', 'OFF');
-- ALTER TABLE stats_users_access_daily  ADD I79 SMALLINT UNSIGNED NOT NULL DEFAULT '0' AFTER I78;

/* ======================= TABLES ======================================================= */

SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";

DROP TABLE IF EXISTS gare_79;
CREATE TABLE IF NOT EXISTS gare_79 (
	agenzia_id					char(4) NOT NULL,
	gruppo						CHAR(2) NOT NULL,
	obiettivoPezzi				MEDIUMINT unsigned NOT NULL default 0,
	obiettivoPremi				DECIMAL(12,2) unsigned NOT NULL default 0 COMMENT 'xtype=MONETARY',
	numeroPolizze				MEDIUMINT unsigned NOT NULL default 0,
	premioComputabile			DECIMAL(12,2) unsigned NOT NULL default 0 COMMENT 'xtype=MONETARY',
	posizione					SMALLINT unsigned NOT NULL default 0,
	PRIMARY KEY (agenzia_id),
	CONSTRAINT fk_iniz79 FOREIGN KEY (agenzia_id) REFERENCES agenzie (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

SET FOREIGN_KEY_CHECKS=1;

/* ======================= VIEWS ======================================================= */

DROP VIEW IF EXISTS vw_gare_79;
CREATE VIEW vw_gare_79 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivoPezzi,
	st.obiettivoPremi,
	st.numeroPolizze,
	st.premioComputabile,
	st.posizione
FROM
	gare_79 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

/* ======================= STORED PROCEDURES =========================================== */

DROP PROCEDURE IF EXISTS gare_79_insert_obiettivi;
DELIMITER //
CREATE PROCEDURE gare_79_insert_obiettivi (
	IN p_iniziativa_id		TINYINT, /* NOT USED */
	IN p_agenzia_id			CHAR(4),
	IN p_gruppo				VARCHAR(2),
	IN p_obiettivoPezzi		MEDIUMINT,
	IN p_obiettivoPremi		DECIMAL(12,2)
)
BEGIN
	INSERT INTO gare_79 (
		agenzia_id,
		gruppo, obiettivoPezzi, obiettivoPremi
	) VALUES (
		p_agenzia_id,
		p_gruppo, p_obiettivoPezzi, p_obiettivoPremi
	) ON DUPLICATE KEY UPDATE
		gruppo = p_gruppo, obiettivoPezzi = p_obiettivoPezzi, obiettivoPremi = p_obiettivoPremi
	;
END; //
DELIMITER ;

DROP PROCEDURE IF EXISTS gare_79_insert_status;
DELIMITER //
CREATE PROCEDURE gare_79_insert_status (
	IN p_iniziativa_id		TINYINT, /* NOT USED */
	IN p_dataEstrazione		DATE,
	IN p_agenzia_id			CHAR(4),
	IN p_numeroPolizze		MEDIUMINT,
	IN p_premioComputabile	DECIMAL(12,2)
)
BEGIN
	SET @gara_79_dataUpdate = p_dataEstrazione;

	UPDATE gare_79 SET
		numeroPolizze = p_numeroPolizze, premioComputabile = p_premioComputabile
		WHERE agenzia_id = p_agenzia_id;

END; //
DELIMITER ;


DROP PROCEDURE IF EXISTS gare_79_set_classifica;
DELIMITER //
CREATE PROCEDURE gare_79_set_classifica (
	IN p_gruppo					CHAR(2)
)
BEGIN
	DECLARE done 						INT default 0;
	DECLARE v_agenzia_id					CHAR(4);
	DECLARE v_posizione					SMALLINT;
	/* CURSORS  */
	DECLARE cur_agenzia CURSOR FOR
		SELECT agenzia_id FROM gare_79
		WHERE gruppo = p_gruppo AND premioComputabile > 0
		ORDER BY premioComputabile DESC, numeroPolizze DESC;
	DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
	/* PROCEDURE */
	SET done = 0;
	SET v_posizione = 0;
	OPEN cur_agenzia;
	FETCH cur_agenzia INTO v_agenzia_id;
	WHILE NOT done DO
		/* set posizione */
		SET v_posizione = v_posizione + 1;
		/* update tables */
		UPDATE gare_79 SET posizione = v_posizione
			WHERE agenzia_id = v_agenzia_id
		;
        FETCH cur_agenzia INTO v_agenzia_id;
    END WHILE;
    CLOSE cur_agenzia;

END; //
DELIMITER ;
