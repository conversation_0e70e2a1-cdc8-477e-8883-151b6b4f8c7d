/* ======================= SETUP ======================================================== */

-- INSERT IGNORE INTO iniz (id, nome, statusAMM, statusAGT, statusINT) VALUES ('54', 'GaraSpecialeAziendeAgricoli2012', 'OFF', 'OFF', 'OFF');
-- ALTER TABLE stats_users_access_daily  ADD I54 SMALLINT UNSIGNED NOT NULL DEFAULT '0' AFTER I53;

/* ======================= TABLES ======================================================= */

SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";

DROP TABLE IF EXISTS gare_54;
CREATE TABLE IF NOT EXISTS gare_54 (
	agenzia_id					char(4) NOT NULL,
	gruppo						CHAR(2) NOT NULL,
	obiettivoPezzi				MEDIUMINT unsigned NOT NULL default 0,
	obiettivoPremi				DECIMAL(12,2) unsigned NOT NULL default 0 COMMENT 'xtype=MONETARY',
	numeroPolizze				MEDIUMINT unsigned NOT NULL default 0,
	premioComputabile			DECIMAL(12,2) unsigned NOT NULL default 0 COMMENT 'xtype=MONETARY',
	posizione					SMALLINT unsigned NOT NULL default 0,
	PRIMARY KEY (agenzia_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='I54 StatusAgenzie';
ALTER TABLE gare_54
	ADD CONSTRAINT fk_iniz54_agenzie FOREIGN KEY (agenzia_id) REFERENCES agenzie (id)
;

SET FOREIGN_KEY_CHECKS=1;

/* ======================= VIEWS ======================================================= */

DROP VIEW IF EXISTS vw_gare_54;
CREATE VIEW vw_gare_54 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivoPezzi,
	st.obiettivoPremi,
	st.numeroPolizze,
	st.premioComputabile,
	st.posizione
FROM
	gare_54 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

/* ======================= STORED PROCEDURES =========================================== */

DROP PROCEDURE IF EXISTS gare_54_insert_obiettivi;
DELIMITER //
CREATE PROCEDURE gare_54_insert_obiettivi (
	IN p_iniziativa_id		TINYINT, /* NOT USED */
	IN p_agenzia_id			CHAR(4),
	IN p_gruppo				VARCHAR(2),
	IN p_obiettivoPezzi		MEDIUMINT,
	IN p_obiettivoPremi		DECIMAL(12,2)
)
BEGIN
	INSERT INTO gare_54 (
		agenzia_id,
		gruppo, obiettivoPezzi, obiettivoPremi
	) VALUES (
		p_agenzia_id,
		p_gruppo, p_obiettivoPezzi, p_obiettivoPremi
	) ON DUPLICATE KEY UPDATE
		gruppo = p_gruppo, obiettivoPezzi = p_obiettivoPezzi, obiettivoPremi = p_obiettivoPremi
	;
END; //
DELIMITER ;

DROP PROCEDURE IF EXISTS gare_54_insert_status;
DELIMITER //
CREATE PROCEDURE gare_54_insert_status (
	IN p_iniziativa_id		TINYINT, /* NOT USED */
	IN p_dataEstrazione		DATE,
	IN p_agenzia_id			CHAR(4),
	IN p_numeroPolizze		MEDIUMINT,
	IN p_premioComputabile	DECIMAL(12,2)
)
BEGIN
	SET @gara_54_dataUpdate = p_dataEstrazione;

	UPDATE gare_54 SET
		numeroPolizze = p_numeroPolizze, premioComputabile = p_premioComputabile
		WHERE agenzia_id = p_agenzia_id;

END; //
DELIMITER ;


DROP PROCEDURE IF EXISTS gare_54_set_classifica;
DELIMITER //
CREATE PROCEDURE gare_54_set_classifica (
	IN p_gruppo					CHAR(2)
)
BEGIN
	DECLARE done 						INT default 0;
	DECLARE v_agenzia_id				CHAR(4);
	DECLARE v_posizione					SMALLINT;
	/* CURSORS  */
	DECLARE cur_agenzia CURSOR FOR
		SELECT agenzia_id FROM gare_54
		WHERE gruppo = p_gruppo AND premioComputabile > 0
		ORDER BY premioComputabile DESC, numeroPolizze DESC;
	DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
	/* PROCEDURE */
	SET done = 0;
	SET v_posizione = 0;
	OPEN cur_agenzia;
	FETCH cur_agenzia INTO v_agenzia_id;
	WHILE NOT done DO
		/* set posizione */
		SET v_posizione = v_posizione + 1;
		/* update tables */
		UPDATE gare_54 SET posizione = v_posizione
			WHERE agenzia_id = v_agenzia_id
		;
        FETCH cur_agenzia INTO v_agenzia_id;
    END WHILE;
    CLOSE cur_agenzia;

END; //
DELIMITER ;
