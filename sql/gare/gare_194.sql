/* ======================= SETUP ======================================================== */

INSERT IGNORE INTO gare (id, nome, url, anno, tutelaBeni) VALUES (194, 'Gara Protezione beni', 'GaraProtezioneBeni', 2018, 1);

/* ======================= TABLES ======================================================= */

SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";

DROP TABLE IF EXISTS gare_194;
CREATE TABLE IF NOT EXISTS gare_194 (
	agenzia_id					CHAR(4) NOT NULL,
	gruppo						CHAR(2) NOT NULL,
	obiettivoPezziQAC			MEDIUMINT UNSIGNED NOT NULL DEFAULT 0,
	obiettivoPremi				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	pezziQAC					MEDIUMINT UNSIGNED NOT NULL DEFAULT 0,
	pezziDIN					MEDIUMINT UNSIGNED NOT NULL DEFAULT 0,
	premi						DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	posizione					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	PRIMARY KEY (agenzia_id),
	CONSTRAINT fk_gare_194 FOREIGN KEY (agenzia_id) REFERENCES agenzie (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

SET FOREIGN_KEY_CHECKS=1;

/* ======================= VIEWS ======================================================= */

DROP VIEW IF EXISTS vw_gare_194;
CREATE VIEW vw_gare_194 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivoPezziQAC,
	st.obiettivoPremi,
	st.pezziQAC,
	st.pezziDIN,
	st.premi,
	st.posizione
FROM
	gare_194 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

/* ======================= STORED PROCEDURES =========================================== */

DROP PROCEDURE IF EXISTS gare_194_insert_obiettivi;
DELIMITER //
CREATE PROCEDURE gare_194_insert_obiettivi (
	IN p_agenzia_id			CHAR(4),
	IN p_gruppo				VARCHAR(2),
	IN p_obiettivoPezzi1	MEDIUMINT UNSIGNED,
	IN p_obiettivoPezzi2	MEDIUMINT UNSIGNED,		-- NOT USED
	IN p_obiettivoPremi1	DECIMAL(12,2) UNSIGNED,
	IN p_obiettivoPremi2	DECIMAL(12,2) UNSIGNED	-- NOT USED
)
BEGIN
	INSERT INTO gare_194 (
		agenzia_id,
		gruppo, obiettivoPezziQAC, obiettivoPremi
	) VALUES (
		p_agenzia_id,
		p_gruppo, p_obiettivoPezzi1, p_obiettivoPremi1
	) ON DUPLICATE KEY UPDATE
		gruppo = p_gruppo, obiettivoPezziQAC = p_obiettivoPezzi1, obiettivoPremi = p_obiettivoPremi1
	;
END; //
DELIMITER ;

DROP PROCEDURE IF EXISTS gare_194_insert_status;
DELIMITER //
CREATE PROCEDURE gare_194_insert_status (
	IN p_dataEstrazione		DATE,
	IN p_agenzia_id			CHAR(4),
	IN p_pezzi1				MEDIUMINT UNSIGNED,
	IN p_pezzi2				MEDIUMINT UNSIGNED,
	IN p_premi1				DECIMAL(12,2) UNSIGNED,
	IN p_premi2				DECIMAL(12,2) UNSIGNED,	-- NOT USED
	IN p_punti				MEDIUMINT UNSIGNED		-- NOT USED
)
BEGIN
	SET @gara_194_dataUpdate = p_dataEstrazione;

	UPDATE gare_194 SET
		pezziQAC = p_pezzi1, pezziDIN = p_pezzi2, premi = p_premi1
		WHERE agenzia_id = p_agenzia_id;

END; //
DELIMITER ;


DROP PROCEDURE IF EXISTS gare_194_set_classifica;
DELIMITER //
CREATE PROCEDURE gare_194_set_classifica (
	IN p_gruppo					CHAR(2)
)
BEGIN
	DECLARE done 						TINYINT UNSIGNED DEFAULT 0;
	DECLARE v_agenzia_id				CHAR(4);
	DECLARE v_posizione					SMALLINT UNSIGNED;
	-- CURSORS
	DECLARE cur_agenzia CURSOR FOR
		SELECT agenzia_id FROM gare_194
		WHERE gruppo = p_gruppo AND premi > 0 AND pezziQAC >= obiettivoPezziQAC AND pezziDIN >= 12 AND premi >= obiettivoPremi
		ORDER BY premi DESC, pezziQAC DESC, pezziDIN DESC;
	DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
	-- PROCEDURE
	UPDATE gare_194 SET posizione = 0 WHERE gruppo = p_gruppo;
	SET done = 0;
	SET v_posizione = 0;
	OPEN cur_agenzia;
	FETCH cur_agenzia INTO v_agenzia_id;
	WHILE NOT done DO
		SET v_posizione = v_posizione + 1;
		UPDATE gare_194 SET posizione = v_posizione
			WHERE agenzia_id = v_agenzia_id
		;
        FETCH cur_agenzia INTO v_agenzia_id;
    END WHILE;
    CLOSE cur_agenzia;

END; //
DELIMITER ;
