
ALTER TABLE agenzie
	ADD			comune_id		int unsigned NULL default NULL			AFTER regione,
	ADD			provincia_id	tinyint unsigned NULL default NULL		AFTER comune_id,
	ADD			regione_id		tinyint unsigned NULL default NULL		AFTER provincia_id
;



DROP PROCEDURE IF EXISTS agenzie_add_geo;
DELIMITER //
CREATE PROCEDURE agenzie_add_geo ()
BEGIN
	DECLARE done 						INT default 0;
	DECLARE v_agenzia_id				CHAR(4);
	DECLARE v_provincia_sigla			CHAR(2);
	DECLARE v_provincia_id				TINYINT unsigned;
	DECLARE v_regione_id				TINYINT unsigned;

	-- CURSORS
	DECLARE cur_agezie CURSOR FOR
		SELECT id, provincia FROM agenzie ORDER BY id;
	DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
	-- PROCEDURE
	SET done = 0;
	OPEN cur_agezie;
	FETCH cur_agezie INTO v_agenzia_id, v_provincia_sigla;
	WHILE NOT done DO

		SET v_provincia_id = ( SELECT id FROM geo_province WHERE sigla = v_provincia_sigla );
		SET v_regione_id = ( SELECT regione_id FROM geo_province WHERE sigla = v_provincia_sigla );

		UPDATE agenzie SET provincia_id = v_provincia_id, regione_id = v_regione_id WHERE id = v_agenzia_id;

		SET done = 0;
		FETCH cur_agezie INTO v_agenzia_id, v_provincia_sigla;
	END WHILE;
    CLOSE cur_agezie;
END; //
DELIMITER ;



CALL agenzie_add_geo();
DROP PROCEDURE IF EXISTS agenzie_add_geo;
