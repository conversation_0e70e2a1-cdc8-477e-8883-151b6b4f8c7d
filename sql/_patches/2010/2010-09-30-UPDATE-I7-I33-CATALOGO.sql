
DROP PROCEDURE IF EXISTS `updateALL_set_users_points`;
DROP PROCEDURE IF EXISTS `users-update_user_points`;
DROP PROCEDURE IF EXISTS `FIX-users-check_user_points`;
DROP PROCEDURE IF EXISTS `updateI7_set_users_points`;

SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";


RENAME TABLE `Catalogo-Premi` TO `Catalogo2009-Premi`;
RENAME TABLE `Catalogo-Ordini` TO `Catalogo2009-Ordini`;
RENAME TABLE `Catalogo-Ordini_status_log` TO `Catalogo2009-Ordini_status_log`;
RENAME TABLE `vw_Catalogo-Ordini` TO `vw_Catalogo2009-Ordini`;
RENAME TABLE `vw_Catalogo-Ordini_status_log` TO `vw_Catalogo2009-Ordini_status_log`;

DROP VIEW IF EXISTS `vw_Catalogo2009-Or<PERSON>i`;
CREATE VIEW `vw_Catalogo2009-<PERSON><PERSON>i` AS
SELECT
	o.*,
	p.codice	AS premioCodice,
	p.nome		AS premioNome,
	p.marca		AS premioMarca,
	p.punti		AS premioPunti,
	u.login		AS userLogin,
	ui.agenziaID AS agenziaID,
	CONCAT(u.cognome, ' ',u.nome) AS userFullName
FROM
	`Catalogo2009-Ordini` o
	LEFT JOIN `Users` u ON ( o.userID = u.id ) LEFT JOIN `Users-Intermediari` ui ON (u.id = ui.id)
	LEFT JOIN `Catalogo2009-Premi` p ON ( o.premioID = p.id )
;

DROP VIEW IF EXISTS `vw_Catalogo2009-Ordini_status_log`;
CREATE VIEW `vw_Catalogo2009-Ordini_status_log` AS
SELECT
	log.*,
	a.nome		AS adminName,
	a.cognome	AS adminSurname,
	CONCAT(a.cognome, ' ',a.nome) AS adminFullName
FROM
	`Catalogo2009-Ordini_status_log` log
	LEFT JOIN `Users` a ON ( log.adminID = a.id )
;


DROP TRIGGER IF EXISTS `tr_Catalogo-Ordini_AFTER_INSERT`;
DROP TRIGGER IF EXISTS `tr_Catalogo2009-Ordini_AFTER_INSERT`;
DELIMITER //
CREATE TRIGGER `tr_Catalogo2009-Ordini_AFTER_INSERT` AFTER INSERT ON `Catalogo2009-Ordini`
	FOR EACH ROW BEGIN		
		CALL `updateI7_set_user_points`(NEW.userID);
	END; //
DELIMITER ;

SET FOREIGN_KEY_CHECKS=1;

