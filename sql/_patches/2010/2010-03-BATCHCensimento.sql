


DROP PROCEDURE IF EXISTS `BATCH_Censimento`;
DELIMITER //
CREATE PROCEDURE `BATCH_Censimento` (
)
BEGIN
	DECLARE done 				INT default 0;
	DECLARE go 					INT default 0;
	DECLARE v_compagnia			CHAR(1);
	DECLARE v_agenziaID			CHAR(4);
	DECLARE v_intermediarioID	VARCHAR(5);
	DECLARE v_nome				VARCHAR(20);
	DECLARE v_cognome			VARCHAR(20);
	DECLARE C_RUI				INT;
	/* CURSORS  */
	DECLARE cur_intermed_OLD CURSOR FOR
		SELECT agenziaID, intermediarioID, nome, cognome FROM `IntermediariOLD`;
	DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;

	/* PROCEDURE */
	SET C_RUI = 0;
	SET done = 0;
	OPEN cur_intermed_OLD;
	FETCH cur_intermed_OLD INTO v_agenziaID, v_intermediarioID, v_nome, v_cognome;
	WHILE NOT done DO

		SET v_compagnia = SUBSTR(v_agenziaID,1,1);
		SET v_agenziaID = SUBSTR(v_agenziaID,2);
		
		SELECT COUNT(*) INTO go
			FROM `IntermediariNEW`
			WHERE compagniaID = v_compagnia AND agenziaID = v_agenziaID AND nome = v_nome AND cognome = v_cognome;
		
		IF go = 1 THEN
			UPDATE `IntermediariNEW`
				SET `intermediarioID` = v_intermediarioID, updatedAt = NULL 
				WHERE  compagniaID = v_compagnia AND agenziaID = v_agenziaID AND nome = v_nome AND cognome = v_cognome;
		ELSE
			SET C_RUI = C_RUI + 1;
			
			INSERT INTO `IntermediariNEW`
				(compagniaID, agenziaID, intermediarioID, nome, cognome, RUI, updatedAt)
				VALUES
				(v_compagnia, v_agenziaID, v_intermediarioID, v_nome, v_cognome, CONCAT('XXX',C_RUI), NULL);
		
		END IF;
		

        FETCH cur_intermed_OLD INTO v_agenziaID, v_intermediarioID, v_nome, v_cognome;
    END WHILE;
    CLOSE cur_intermed_OLD;
	
END; //
DELIMITER ;

