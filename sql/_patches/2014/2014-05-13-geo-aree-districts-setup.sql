INSERT INTO geo_aree (id, codice, nome) VALUES
	(1, 'NO', 'Nord-Ovest'),
	(2, 'N', 'Nord'),
	(3, 'NE', 'Nord-Est'),
	(4, 'CN', 'Centro-Nord'),
	(5, 'RM', 'Roma & Prov.'),
	(6, '', 'Agenzie di Direzione'),
	(7, 'SO', 'Sud-Ovest'),
	(8, 'C', 'Centro'),
	(9, '', 'Varie'),
	(10, 'SE', 'Sud-Est')
;

INSERT INTO geo_districts (id, area_id, nome) SELECT district, area, CONCAT(cognome, ' ', nome) AS nome FROM users WHERE type = 'DISTRICTMGR';



DROP TRIGGER IF EXISTS triggerBI_users;
DELIMITER //
CREATE TRIGGER triggerBI_users BEFORE INSERT ON users
	FOR EACH ROW BEGIN
		IF NEW.type IN ( 'AGENTE','INTERMEDIARIO' ) THEN
			SET NEW.area = ( SELECT area FROM agenzie WHERE id = NEW.agenzia_id );
			SET NEW.district  = ( SELECT district FROM agenzie WHERE id = NEW.agenzia_id );
		END IF;
		-- GEO DISTRICT auto generation
		IF NEW.type = 'DISTRICTMGR' THEN
			INSERT INTO geo_districts (id, area_id, nome) VALUES (NEW.district, NEW.area, CONCAT(NEW.cognome, ' ', NEW.nome));
		END IF;
	END; //
DELIMITER ;


DROP TRIGGER IF EXISTS triggerBU_users;
DELIMITER //
CREATE TRIGGER triggerBU_users BEFORE UPDATE ON users
	FOR EACH ROW BEGIN
		IF NEW.type IN ( 'AGENTE','INTERMEDIARIO' ) THEN
			SET NEW.area = ( SELECT area FROM agenzie WHERE id = NEW.agenzia_id );
			SET NEW.district  = ( SELECT district FROM agenzie WHERE id = NEW.agenzia_id );
		END IF;
		-- GEO DISTRICT auto generation
		IF NEW.type = 'DISTRICTMGR' AND OLD.type = 'DISTRICTMGR' AND OLD.district != NEW.district THEN
			INSERT INTO geo_districts (id, area_id, nome) VALUES (NEW.district, NEW.area, CONCAT(NEW.cognome, ' ', NEW.nome));
			DELETE FROM geo_districts WHERE id = OLD.district;
		END IF;
		IF NEW.type = 'DISTRICTMGR' AND OLD.type != 'DISTRICTMGR' THEN
			INSERT INTO geo_districts (id, area_id, nome) VALUES (NEW.district, NEW.area, CONCAT(NEW.cognome, ' ', NEW.nome));
		END IF;
		IF NEW.type != 'DISTRICTMGR' AND OLD.type = 'DISTRICTMGR' THEN
			DELETE FROM geo_districts WHERE id = OLD.district;
		END IF;
	END; //
DELIMITER ;


DROP TRIGGER IF EXISTS triggerAD_users;
DELIMITER //
CREATE TRIGGER triggerAD_users AFTER DELETE ON users
	FOR EACH ROW BEGIN
		-- GEO DISTRICT auto generation
		IF OLD.type = 'DISTRICTMGR' THEN
			DELETE FROM geo_districts WHERE id = OLD.district;
		END IF;
	END; //
DELIMITER ;

