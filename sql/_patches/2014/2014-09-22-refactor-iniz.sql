DROP VIEW IF EXISTS vw_iniz_90;
CREATE VIEW vw_iniz_90 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.premiDimCap,
	st.premiDimQuo,
	st.premiDimInv,
	st.premiDimFreeInv,
	st.premiTOTALI,
	st.pezziDimCap,
	st.pezziDimQuo,
	st.pezziDimInv,
	st.pezziDimFreeInv,
	st.pezziTOTALI,
	st.premiCompDimCap,
	st.premiCompDimQuo,
	st.premiCompDimInv,
	st.premiCompDimFreeInv,
	st.premiCompTOTALI,
	st.obiettivoOK,
	st.bonusOK,
	st.importoErog,
	st.importoErogBonus,
	st.importoErogTOTALE,
	st.objGAshow,
	st.objGAobjPremi,
--	st.obj<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	st.objGApercPremi,
--	st.obj<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	st.objGAstatus,
	st.objGApercStatus
FROM
	iniz_90 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_89;
CREATE VIEW vw_iniz_89 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.pezziTOTALI,
	st.premiTOTALI,
	st.premiCompTOTALI,
	st.bonusPerc,
	st.importoErogabile,
	st.objGAshow,
	st.objGAobjPremi,
--	st.objGAobjPezzi,
	st.objGApercPremi,
--	st.objGApercPezzi,
	st.objGAstatus,
	st.objGApercStatus
FROM
	iniz_89 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_88;
CREATE VIEW vw_iniz_88 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.obiettivo,
	st.jolly,
	st.premiBloccaPrezzo,
	st.premiBlackBox,
	st.premiBonusDuetto,
	st.premiBonusFull,
	st.premiTOTALI,
	st.pezziBloccaPrezzo,
	st.pezziBlackBox,
	st.pezziBonusDuetto,
	st.pezziBonusFull,
	st.pezziTOTALI,
	st.premiCompBloccaPrezzo,
	st.premiCompBlackBox,
	st.premiCompBonusDuetto,
	st.premiCompBonusFull,
	st.premiCompTOTALI,
	st.obiettivoOK,
	st.percWelcomeback,
	st.bonusOK,
	st.importoErogBloccaPrezzo,
	st.importoErogBlackBox,
	st.importoErogBonusDuetto,
	st.importoErogBonusFull,
	st.importoErogTOTALE,
	st.objGAshow,
	st.objGAobjPremi,
--	st.objGAobjPezzi,
	st.objGApercPremi,
--	st.objGApercPezzi,
	st.objGAstatus,
	st.objGApercStatus
FROM
	iniz_88 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_87;
CREATE VIEW vw_iniz_87 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.pezziMS,
	st.pezziDimStu,
	st.pezziDimRis,
	st.pezziDimPro,
	st.pezziDimTut,
	st.pezziDIM,
	st.pezziTOTALI,
	st.premiMS,
	st.premiDimStu,
	st.premiDimRis,
	st.premiDimPro,
	st.premiDimTut,
	st.premiDIM,
	st.premiTOTALI,
	st.premiCompMS,
	st.premiCompDimStu,
	st.premiCompDimRis,
	st.premiCompDimPro,
	st.premiCompDimTut,
	st.premiCompDIM,
	st.premiCompTOTALI,
	st.obiettivoOK,
	st.bonusOK,
	st.bonusPerc,
	st.importoErogMS,
	st.importoErogALL,
	st.importoErogabile,
	st.objGAshow,
	st.objGAobjPremi,
--	st.objGAobjPezzi,
	st.objGApercPremi,
--	st.objGApercPezzi,
	st.objGAstatus,
	st.objGApercStatus
FROM
	iniz_87 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_86;
CREATE VIEW vw_iniz_86 AS
SELECT
	i.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	i.gruppo,
	i.obiettivo,
	i.clientiARGENTO,
	i.clientiORO,
	i.clientiPLATINO,
	i.clientiTotali,
	i.obiettivoOK,
	i.importoErogabile,
	i.objGAshow,
--	i.objGAobjPremi,
	i.objGAobjPezzi,
--	i.objGApercPremi,
	i.objGApercPezzi,
	i.objGAstatus,
	i.objGApercStatus
FROM
	iniz_86 i
	LEFT JOIN agenzie ag ON ( i.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_85;
CREATE VIEW vw_iniz_85 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.premiDimCap,
	st.premiDimQuo,
	st.premiDimInv,
	st.premiDimFreeInv,
	st.premiTOTALI,
	st.pezziDimCap,
	st.pezziDimQuo,
	st.pezziDimInv,
	st.pezziDimFreeInv,
	st.pezziTOTALI,
	st.premiCompDimCap,
	st.premiCompDimQuo,
	st.premiCompDimInv,
	st.premiCompDimFreeInv,
	st.premiCompTOTALI,
	st.obiettivoOK,
	st.bonusOK,
	st.importoErog,
	st.importoErogBonus,
	st.importoErogTOTALE,
	st.objGAshow,
	st.objGAobjPremi,
--	st.objGAobjPezzi,
	st.objGApercPremi,
--	st.objGApercPezzi,
	st.objGAstatus,
	st.objGApercStatus
FROM
	iniz_85 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_84;
CREATE VIEW vw_iniz_84 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivoCFS,
	st.obiettivoQAC,
	st.pezziCFS,
	st.pezziQAC,
	st.pezziTOTALI,
	st.premiCFS,
	st.premiQAC,
	st.premiTOTALI,
	st.premiCompCFS,
	st.premiCompQAC,
	st.premiCompTOTALI,
	st.obiettivoCfsOK,
	st.obiettivoQacOK,
	st.percIncent,
	st.importoErogabile,
	st.objGAshow,
	st.objGAobjPremi,
--	st.objGAobjPezzi,
	st.objGApercPremi,
--	st.objGApercPezzi,
	st.objGAstatus,
	st.objGApercStatus
FROM
	iniz_84 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_83;
CREATE VIEW vw_iniz_83 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivo,
	st.pezziPack,
	st.premiPack,
	st.pezziPluri,
	st.premiPluri,
	st.obiettivoOK,
	st.bonusOK,
	st.importoErogPack,
	st.importoErogPluri,
	st.importoErogTOTALE,
	st.objGAshow,
	st.objGAobjPremi,
--	st.objGAobjPezzi,
	st.objGApercPremi,
--	st.objGApercPezzi,
	st.objGAstatus,
	st.objGApercStatus
FROM
	iniz_83 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_82;
CREATE VIEW vw_iniz_82 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.punti,
	st.posizione
FROM
	iniz_82 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_80;
CREATE VIEW vw_iniz_80 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivoPezzi,
	st.obiettivoPremi,
	st.numeroPolizze,
	st.premioComputabile,
	st.posizione
FROM
	iniz_80 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_79;
CREATE VIEW vw_iniz_79 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivoPezzi,
	st.obiettivoPremi,
	st.numeroPolizze,
	st.premioComputabile,
	st.posizione
FROM
	iniz_79 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_78;
CREATE VIEW vw_iniz_78 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivoPezzi,
	st.obiettivoPremi,
	st.numeroPolizze,
	st.premioComputabile,
	st.posizione
FROM
	iniz_78 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_77;
CREATE VIEW vw_iniz_77 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.active,
	st.soglia1,
	st.soglia2,
	st.pezzi1,
	st.pezzi2,
	st.pezziTOT,
	st.importoErog1,
	st.importoErog2,
	st.importoErogTOTALE,
	st.fascia1,
	st.fascia2,
	st.fascia3
FROM
	iniz_77 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_76;
CREATE VIEW vw_iniz_76 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.pezziAN,
	st.pezziVA,
	st.pezziTOTALI,
	st.premiAN,
	st.premiVA,
	st.premiTOTALI,
	st.premiCompAN,
	st.premiCompVA,
	st.premiCompTOTALI,
	st.bonusPerc,
	st.importoErogabile,
	st.objGAshow,
	st.objGAobjPremi,
--	st.objGAobjPezzi,
	st.objGApercPremi,
--	st.objGApercPezzi,
	st.objGAstatus,
	st.objGApercStatus
FROM
	iniz_76 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_75;
CREATE VIEW vw_iniz_75 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.premiDimBonus,
	st.premiDimInv,
	st.premiDimFreeInv,
	st.premiDimPiuQ,
	st.premiTOTALI,
	st.pezziDimBonus,
	st.pezziDimInv,
	st.pezziDimFreeInv,
	st.pezziDimPiuQ,
	st.pezziTOTALI,
	st.premiCompDimBonus,
	st.premiCompDimInv,
	st.premiCompDimFreeInv,
	st.premiCompDimPiuQ,
	st.premiCompTOTALI,
	st.perc,
	st.bonusOK,
	st.importoErogabile,
	st.objGAshow,
	st.objGAobjPremi,
--	st.objGAobjPezzi,
	st.objGApercPremi,
--	st.objGApercPezzi,
	st.objGAstatus,
	st.objGApercStatus
FROM
	iniz_75 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_74;
CREATE VIEW vw_iniz_74 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.pezziMS,
	st.pezziDimStu,
	st.pezziDimRis,
	st.pezziDimPro,
	st.pezziDimTut,
	st.pezziDIM,
	st.pezziTOTALI,
	st.premiMS,
	st.premiDimStu,
	st.premiDimRis,
	st.premiDimPro,
	st.premiDimTut,
	st.premiDIM,
	st.premiTOTALI,
	st.premiCompMS,
	st.premiCompDimStu,
	st.premiCompDimRis,
	st.premiCompDimPro,
	st.premiCompDimTut,
	st.premiCompDIM,
	st.premiCompTOTALI,
	st.obiettivoOK,
	st.bonusOK,
	st.bonusPerc,
	st.importoErogMS,
	st.importoErogDIM,
	st.importoErogabile,
	st.objGAshow,
	st.objGAobjPremi,
--	st.objGAobjPezzi,
	st.objGApercPremi,
--	st.objGApercPezzi,
	st.objGAstatus,
	st.objGApercStatus
FROM
	iniz_74 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_73;
CREATE VIEW vw_iniz_73 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivoCFS,
	st.obiettivoQAC,
	st.pezziCFS,
	st.pezziQAC,
	st.pezziTOTALI,
	st.premiCFS,
	st.premiQAC,
	st.premiTOTALI,
	st.premiCompCFS,
	st.premiCompQAC,
	st.premiCompTOTALI,
	st.obiettivoCfsOK,
	st.obiettivoQacOK,
	st.percIncent,
	st.importoErogabile,
	st.objGAshow,
	st.objGAobjPremi,
--	st.objGAobjPezzi,
	st.objGApercPremi,
--	st.objGApercPezzi,
	st.objGAstatus,
	st.objGApercStatus
FROM
	iniz_73 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_72;
CREATE VIEW vw_iniz_72 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.ptf2012,
	st.ptf2013,
	st.obiettivo,
	st.pezziINF,
	st.pezziTUT,
	st.pezziTOTALI,
	st.premiINF,
	st.premiTUT,
	st.premiTOTALI,
	st.premiCompINF,
	st.premiCompTUT,
	st.premiCompTOTALI,
	st.obiettivoOK,
	st.bonusOK,
	st.importoErogINF,
	st.importoErogTUT,
	st.importoErogabile,
	st.objGAshow,
	st.objGAobjPremi,
--	st.objGAobjPezzi,
	st.objGApercPremi,
--	st.objGApercPezzi,
	st.objGAstatus,
	st.objGApercStatus
FROM
	iniz_72 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_71;
CREATE VIEW vw_iniz_71 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.premiDim,
	st.premiDimFree,
	st.premiDimPlus,
	st.premiTOTALI,
	st.pezziDim,
	st.pezziDimFree,
	st.pezziDimPlus,
	st.pezziTOTALI,
	st.premiCompDim,
	st.premiCompDimFree,
	st.premiCompDimPlus,
	st.premiCompTOTALI,
	st.bonus,
	st.importoErogabile,
	st.objGAshow,
	st.objGAobjPremi,
--	st.objGAobjPezzi,
	st.objGApercPremi,
--	st.objGApercPezzi,
	st.objGAstatus,
	st.objGApercStatus
FROM
	iniz_71 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_70;
CREATE VIEW vw_iniz_70 AS
SELECT
	i.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	i.gruppo,
	i.obiettivo,
	i.clientiARGENTO,
	i.clientiORO,
	i.clientiPLATINO,
	i.clientiTotali,
	i.obiettivoOK,
	i.importoErogabile,
	i.objGAshow,
--	i.objGAobjPremi,
	i.objGAobjPezzi,
--	i.objGApercPremi,
	i.objGApercPezzi,
	i.objGAstatus,
	i.objGApercStatus
FROM
	iniz_70 i
	LEFT JOIN agenzie ag ON ( i.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_69;
CREATE VIEW vw_iniz_69 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.premiMS,
	st.premiDimStu,
	st.premiDimRis,
	st.premiDimPro,
	st.premiDimTut,
	st.premiTOTALI,
	st.pezziMS,
	st.pezziDimStu,
	st.pezziDimRis,
	st.pezziDimPro,
	st.pezziDimTut,
	st.pezziTOTALI,
	st.premiCompMS,
	st.premiCompDimStu,
	st.premiCompDimRis,
	st.premiCompDimPro,
	st.premiCompDimTut,
	st.premiCompTOTALI,
	st.obiettivoOK,
	st.bonusOK,
	st.importoErogMS,
	st.importoErogDim,
	st.importoErogabile,
	st.objGAshow,
	st.objGAobjPremi,
--	st.objGAobjPezzi,
	st.objGApercPremi,
--	st.objGApercPezzi,
	st.objGAstatus,
	st.objGApercStatus
FROM
	iniz_69 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_68;
CREATE VIEW vw_iniz_68 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivoPezzi,
	st.obiettivoPremi,
	st.numeroPolizze,
	st.premioComputabile,
	st.posizione
FROM
	iniz_68 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_67;
CREATE VIEW vw_iniz_67 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivoPezzi,
	st.obiettivoPremi,
	st.numeroPolizze,
	st.premioComputabile,
	st.posizione
FROM
	iniz_67 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_66;
CREATE VIEW vw_iniz_66 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivoPezzi,
	st.obiettivoPremi,
	st.numeroPolizze,
	st.premioComputabile,
	st.posizione
FROM
	iniz_66 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_65;
CREATE VIEW vw_iniz_65 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.punti,
	st.posizione
FROM
	iniz_65 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_64;
CREATE VIEW vw_iniz_64 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.premiDim,
	st.premiDimFree,
	st.premiDimPlus,
	st.premiTOTALI,
	st.pezziDim,
	st.pezziDimFree,
	st.pezziDimPlus,
	st.pezziTOTALI,
	st.premiCompDim,
	st.premiCompDimFree,
	st.premiCompDimPlus,
	st.premiCompTOTALI,
	st.bonus,
	st.importoErogabile,
	st.objGAshow,
	st.objGAobjPremi,
--	st.objGAobjPezzi,
	st.objGApercPremi,
--	st.objGApercPezzi,
	st.objGAstatus,
	st.objGApercStatus
FROM
	iniz_64 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_63;
CREATE VIEW vw_iniz_63 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivo,
	st.pezziMS,
	st.premiMS,
	st.premiCompMS,
	st.pezziPLINF,
	st.premiPLINF,
	st.premiCompPLINF,
	st.pezziTOTALI,
	st.premiTOTALI,
	st.premiCompTOTALI,
	st.numPacchetti,
	st.obiettivoOK,
	st.importoErogabile,
	st.objGAshow,
--	st.objGAobjPremi,
	st.objGAobjPezzi,
--	st.objGApercPremi,
	st.objGApercPezzi,
	st.objGAstatus,
	st.objGApercStatus
FROM
	iniz_63 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_62;
CREATE VIEW vw_iniz_62 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.premiMSa,
	st.premiMSu,
	st.premiMS,
	st.premiDIMPT,
	st.premiTOTALI,
	st.pezziMSa,
	st.pezziMSu,
	st.pezziMS,
	st.pezziDIMPT,
	st.pezziTOTALI,
	st.premiCompMS,
	st.premiCompDIMPT,
	st.premiCompTOTALI,
	st.obiettivoPremi,
	st.obiettivoPezzi,
	st.obiettivo,
	st.bonus,
	st.importoErogabileMS,
	st.importoErogabileDIMPT,
	st.importoErogabileBonus,
	st.importoErogabile,
	st.objGAshow,
	st.objGAobjPremi,
--	st.objGAobjPezzi,
	st.objGApercPremi,
--	st.objGApercPezzi,
	st.objGAstatus,
	st.objGApercStatus
FROM
	iniz_62 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_61;
CREATE VIEW vw_iniz_61 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.premiDIMINV,
	st.premiCAPITAL,
	st.premiDIMPQ,
	st.premiTOTALI,
	st.pezziDIMINV,
	st.pezziCAPITAL,
	st.pezziDIMPQ,
	st.pezziTOTALI,
	st.premiCompDIMINV,
	st.premiCompCAPITAL,
	st.premiCompDIMPQ,
	st.premiCompTOTALI,
	st.obiettivo,
	st.percObiettivo,
	st.bonusDIMINV,
	st.bonusCAPITAL,
	st.bonusDIMPQ,
	st.importoErogDIMINV,
	st.importoErogCAPITAL,
	st.importoErogDIMPQ,
	st.importoErogTOTALE,
	st.superBonus,
	st.objGAshow,
	st.objGAobjPremi,
--	st.objGAobjPezzi,
	st.objGApercPremi,
--	st.objGApercPezzi,
	st.objGAstatus,
	st.objGApercStatus
FROM
	iniz_61 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_60;
CREATE VIEW vw_iniz_60 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.premiDIMINV,
	st.premiDIMPQ,
	st.premiTOTALI,
	st.pezziDIMINV,
	st.pezziDIMPQ,
	st.pezziTOTALI,
	st.premiCompDIMINV,
	st.premiCompDIMPQ,
	st.premiCompTOTALI,
	st.obiettivo,
	st.percObiettivo,
	st.pesoDPQ,
	st.bonus,
	st.importoErogabile,
	st.objGAshow,
	st.objGAobjPremi,
--	st.objGAobjPezzi,
	st.objGApercPremi,
--	st.objGApercPezzi,
	st.objGAstatus,
	st.objGApercStatus
FROM
	iniz_60 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_59;
CREATE VIEW vw_iniz_59 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivo,
	st.polizzeQAC,
	st.premiQAC,
	st.polizzeDOI,
	st.premiDOI,
	st.polizzeTOT,
	st.premiTOT,
	st.obiettivoOK,
	st.importoErogabile
FROM
	iniz_59 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_58;
CREATE VIEW vw_iniz_58 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivoGA200,

	st.polizzeGA200,
	st.premiGA200,
	st.importoErogabileGA200,
	st.polizzePLINF,
	st.premiPLINF,
	st.importoErogabilePLINF,

	st.importoErogTOTALE,

	st.objGAshow,
	st.objGAobjPremi,
--	st.objGAobjPezzi,
	st.objGApercPremi,
--	st.objGApercPezzi,
	st.objGAstatus,
	st.objGApercStatus
FROM
	iniz_58 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_57;
CREATE VIEW vw_iniz_57 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivoPezzi,
	st.obiettivoPremi,
	st.numeroPolizze,
	st.premioComputabile,
	st.posizione
FROM
	iniz_57 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_56;
CREATE VIEW vw_iniz_56 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivoPezzi,
	st.obiettivoPremi,
	st.numeroPolizze,
	st.premioComputabile,
	st.posizione
FROM
	iniz_56 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_55;
CREATE VIEW vw_iniz_55 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.punti,
	st.posizione
FROM
	iniz_55 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_54;
CREATE VIEW vw_iniz_54 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivoPezzi,
	st.obiettivoPremi,
	st.numeroPolizze,
	st.premioComputabile,
	st.posizione
FROM
	iniz_54 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_53;
CREATE VIEW vw_iniz_53 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.polizzeF1_MS,
	st.premiF1_MS,
	st.obiettivoF1,
	st.importoErogabileF1,
	st.polizzeF2_MS,
	st.premiF2_MS,
	st.polizzeF2_DIM,
	st.premiF2_DIM,
	st.obiettivoF2,
	st.importoErogabileF2_MS,
	st.importoErogabileF2_DIM,
	st.importoErogabileF2,
	st.obiettivoBonus,
	st.importoBonus
FROM
	iniz_53 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_52;
CREATE VIEW vw_iniz_52 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.premiDIMINV,
	st.premiCAPITAL,
	st.premiDIMPQ,
	st.premiTOTALI,
	st.pezziDIMINV,
	st.pezziCAPITAL,
	st.pezziDIMPQ,
	st.pezziTOTALI,
	st.premiCompDIMINV,
	st.premiCompCAPITAL,
	st.premiCompDIMPQ,
	st.premiCompTOTALI,
	st.obiettivo,
	st.percObiettivo,
	st.bonusDIMINV,
	st.bonusCAPITAL,
	st.bonusDIMPQ,
	st.importoErogDIMINV,
	st.importoErogCAPITAL,
	st.importoErogDIMPQ,
	st.importoErogTOTALE,
	st.superBonus,
	st.objGAshow,
	st.objGAobjPremi,
--	st.objGAobjPezzi,
	st.objGApercPremi,
--	st.objGApercPezzi,
	st.objGAstatus,
	st.objGApercStatus
FROM
	iniz_52 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS `vw_iniz_51`;
CREATE VIEW `vw_iniz_51` AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.polizze_MS,
	st.premi_MS,
	st.polizze_DIM,
	st.premi_DIM,
	st.obiettivo,
	st.importoErogabile_MS,
	st.importoErogabile_DIM,
	st.importoErogabile
FROM
	`iniz_51` st
	LEFT JOIN `agenzie` ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_50;
CREATE VIEW vw_iniz_50 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.premiDIMINV,
	st.premiCAPITAL,
	st.premiDIMPQ,
	st.premiTOTALI,
	st.pezziDIMINV,
	st.pezziCAPITAL,
	st.pezziDIMPQ,
	st.pezziTOTALI,
	st.premiCompDIMINV,
	st.premiCompCAPITAL,
	st.premiCompDIMPQ,
	st.premiCompTOTALI,
	st.obiettivo,
	st.percObiettivo,
	st.bonusDIMINV,
	st.bonusCAPITAL,
	st.bonusDIMPQ,
	st.importoErogDIMINV,
	st.importoErogCAPITAL,
	st.importoErogDIMPQ,
	st.importoErogTOTALE,
	st.objGAshow,
	st.objGAobjPremi,
--	st.objGAobjPezzi,
	st.objGApercPremi,
--	st.objGApercPezzi,
	st.objGAstatus,
	st.objGApercStatus
FROM
	iniz_50 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS `vw_iniz_48`;
CREATE VIEW `vw_iniz_48` AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.pezziPACKINF,
	st.premiPACKINF,
	st.pezziPLURIATT,
	st.premiPLURIATT,
	st.obiettivo,
	st.percObiettivo,
	st.importoErogPACKINF,
	st.importoErogPLURIATT,
	st.importoErogTOTALE,
	st.posizioneGruppo,
	st.winnerGruppo,
	st.posizioneNazionale,
	st.winnerNazionale
FROM
	`iniz_48` st
	LEFT JOIN `agenzie` ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_47;
CREATE VIEW vw_iniz_47 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.premiDIMINV,
	st.premiCAPITAL,
	st.premiDIMPQ,
	st.premiTOTALI,
	st.pezziDIMINV,
	st.pezziCAPITAL,
	st.pezziDIMPQ,
	st.pezziTOTALI,
	st.premiCompDIMINV,
	st.premiCompCAPITAL,
	st.premiCompDIMPQ,
	st.premiCompTOTALI,
	st.obiettivo,
	st.percObiettivo,
	st.bonusDIMINV,
	st.bonusCAPITAL,
	st.bonusDIMPQ,
	st.importoErogDIMINV,
	st.importoErogCAPITAL,
	st.importoErogDIMPQ,
	st.importoErogTOTALE,
	st.objGAshow,
	st.objGAobjPremi,
--	st.objGAobjPezzi,
	st.objGApercPremi,
--	st.objGApercPezzi,
	st.objGAstatus,
	st.objGApercStatus
FROM
	iniz_47 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS `vw_iniz_45`;
CREATE VIEW `vw_iniz_45` AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivoPezzi,
	st.obiettivoPremi,
	st.numeroPolizze,
	st.premioComputabile,
	st.posizione
FROM
	`iniz_45` st
	LEFT JOIN `agenzie` ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS `vw_iniz_44`;
CREATE VIEW `vw_iniz_44` AS
	SELECT
		st.agenzia_id,
		ag.area,
		ag.district,
		ag.localita,
		ag.nome,
		ag.status,
		st.gruppo,
		st.obiettivoPezzi,
		st.obiettivoPremi,
		st.numeroPolizze,
		st.premioComputabile,
		st.posizione
	FROM
		`iniz_44` st
		LEFT JOIN `agenzie` ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS `vw_iniz_43`;
CREATE VIEW `vw_iniz_43` AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivoPezzi,
	st.obiettivoPremi,
	st.numeroPolizze,
	st.premioComputabile,
	st.posizione
FROM
	`iniz_43` st
	LEFT JOIN `agenzie` ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS `vw_iniz_42`;
CREATE VIEW `vw_iniz_42` AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivoPezzi,
	st.obiettivoPremi,
	st.numeroPolizze,
	st.premioComputabile,
	st.posizione
FROM
	`iniz_42` st
	LEFT JOIN `agenzie` ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS `vw_iniz_41`;
CREATE VIEW `vw_iniz_41` AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.punti,
	st.posizione
FROM
	`iniz_41` st
	LEFT JOIN `agenzie` ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS `vw_iniz_40`;
CREATE VIEW `vw_iniz_40` AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.obiettivoSP,
	st.obiettivoPV,
	st.premiDIMINV,
	st.premiCompDIMINV,
	st.premiCAPITAL,
	st.premiDIMPQ,
	st.premiTOTALI,
	st.obiettivoSP_OK,
	st.obiettivoPV_OK,
	st.bonusDIMINV,
	st.bonusCAPITAL,
	st.bonusDIMPQ,
	st.importoErogDIMINV,
	st.importoErogCAPITAL,
	st.importoErogDIMPQ,
	st.importoErogTOTALE
FROM
	`iniz_40` st
	LEFT JOIN `agenzie` ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS `vw_iniz_39`;
CREATE VIEW `vw_iniz_39` AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.obiettivoMese,
	st.premi01,
	st.obiettivo01OK,
	st.importoErogabile01,
	st.premi02,
	st.obiettivo02OK,
	st.importoErogabile02,
	st.premi0304,
	st.obiettivo0304OK,
	st.importoErogabile0304,
	st.premi05,
	st.obiettivo05OK,
	st.importoErogabile05,
	st.premi06,
	st.obiettivo06OK,
	st.importoErogabile06,
	st.premi07,
	st.obiettivo07OK,
	st.importoErogabile07,
	st.premi08,
	st.obiettivo08OK,
	st.importoErogabile08,
	st.premi09,
	st.obiettivo09OK,
	st.importoErogabile09,
	st.premi10,
	st.obiettivo10OK,
	st.importoErogabile10,
	st.premi11,
	st.obiettivo11OK,
	st.importoErogabile11,
	st.premi12,
	st.obiettivo12OK,
	st.importoErogabile12,
	st.punti,
	st.bonus,
	st.totImportoErogabile
FROM
	`iniz_39` st
	LEFT JOIN `agenzie` ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS `vw_iniz_38`;
CREATE VIEW `vw_iniz_38` AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.active,
	st.polizze_MS,
	st.polizze_DIM,
	st.premi_MS,
	st.premi_DIM,
	st.obiettivoOK,
	st.importoErogabile,
	st.bonusOK
FROM
	`iniz_38` st
	LEFT JOIN `agenzie` ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS `vw_iniz_37`;
CREATE VIEW `vw_iniz_37` AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.polizzeA1_MS,
	st.premiA1_MS,
	st.obiettivoA1,
	st.importoErogabileA1,
	st.polizzeA2_DIM,
	st.premiA2_DIM,
	st.obiettivoA2,
	st.importoErogabileA2,
	st.polizzeA3_MS,
	st.premiA3_MS,
	st.polizzeA3_DIM,
	st.premiA3_DIM,
	st.obiettivoA3,
	st.importoErogabileA3_MS,
	st.importoErogabileA3_DIM,
	st.importoErogabileA3,
	st.obiettivoB1,
	st.importoB1,
	st.obiettivoB2,
	st.importoB2,
	st.obiettivoB3,
	st.importoB3,
	st.obiettivoB4
FROM
	`iniz_37` st
	LEFT JOIN `agenzie` ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS `vw_iniz_35`;
CREATE VIEW `vw_iniz_35` AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.numeroPolizze,
	st.totPremiComputabili,
	st.percentualeObiettivo,
	SIGN(TRUNCATE((st.percentualeObiettivo/100),0)) AS obiettivoOK,
	st.bonus,
	st.importoErogabile
FROM
	`iniz_35` st
	LEFT JOIN `agenzie` ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS `vw_iniz_34`;
CREATE VIEW `vw_iniz_34` AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivo,
	st.numeroPolizze,
	st.totPremiComputabili,
	st.percentualeObiettivo,
	SIGN(TRUNCATE((st.percentualeObiettivo/100),0)) AS obiettivoOK,
	st.importoErogabile
FROM
	`iniz_34` st
	LEFT JOIN `agenzie` ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS `vw_iniz_32`;
CREATE VIEW `vw_iniz_32` AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.numeroPolizze,
	st.totPremiComputabili,
	st.bonus,
	st.importoErogabile
FROM
	`iniz_32` st
	LEFT JOIN `agenzie` ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS `vw_iniz_31`;
CREATE VIEW `vw_iniz_31` AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.obiettivo,
	st.numeroPolizze,
	st.totPremiComputabili,
	st.percentualeObiettivo,
	SIGN(TRUNCATE((st.percentualeObiettivo/100),0)) AS obiettivoOK,
	st.importoErogabile
FROM
	`iniz_31` st
	LEFT JOIN `agenzie` ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS `vw_iniz_30`;
CREATE VIEW `vw_iniz_30` AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.numeroPolizze,
	st.premiCapital,
	st.premiDimensione,
	st.premiCapitalComputabili,
	st.premiDimensioneComputabili,
	st.totPremi,
	st.totPremiComputabili,
	st.obiettivo,
	st.percentualeObiettivo,
	SIGN(TRUNCATE((st.percentualeObiettivo/100),0)) AS obiettivoOK,
	st.importoErogabile
FROM
	`iniz_30` st
	LEFT JOIN `agenzie` ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS `vw_iniz_29`;
CREATE VIEW `vw_iniz_29` AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivo,
	st.numeroPolizze,
	st.totPremiComputabili,
	st.percentualeObiettivo,
	SIGN(TRUNCATE((st.percentualeObiettivo/100),0)) AS obiettivoOK,
	st.importoErogabile
FROM
	`iniz_29` st
	LEFT JOIN `agenzie` ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS `vw_iniz_28`;
CREATE VIEW `vw_iniz_28` AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivoPezzi,
	st.obiettivoPremi,
	st.numeroPolizze,
	st.premioComputabile,
	st.posizione
FROM
	`iniz_28` st
	LEFT JOIN `agenzie` ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS `vw_iniz_27`;
CREATE VIEW `vw_iniz_27` AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivoPezzi,
	st.obiettivoPremi,
	st.numeroPolizze,
	st.premioComputabile,
	st.posizione
FROM
	`iniz_27` st
	LEFT JOIN `agenzie` ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS `vw_iniz_26`;
CREATE VIEW `vw_iniz_26` AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivoPezzi,
	st.obiettivoPremi,
	st.numeroPolizze,
	st.premioComputabile,
	st.posizione
FROM
	`iniz_26` st
	LEFT JOIN `agenzie` ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS `vw_iniz_25`;
CREATE VIEW `vw_iniz_25` AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivoPezzi,
	st.obiettivoPremi,
	st.numeroPolizze,
	st.premioComputabile,
	st.posizione
FROM
	`iniz_25` st
	LEFT JOIN `agenzie` ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS `vw_iniz_24`;
CREATE VIEW `vw_iniz_24` AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.punti,
	st.posizione
FROM
	`iniz_24` st
	LEFT JOIN `agenzie` ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS `vw_iniz_23`;
CREATE VIEW `vw_iniz_23` AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivo,
	st.numeroPolizze,
	st.totPremiComputabili,
	st.percentualeObiettivo,
	SIGN(TRUNCATE((st.percentualeObiettivo/100),0)) AS obiettivoOK,
	st.importoErogabile
FROM
	`iniz_23` st
	LEFT JOIN `agenzie` ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS `vw_iniz_22`;
CREATE VIEW `vw_iniz_22` AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.numeroPolizze,
	st.totPremiComputabili,
	st.bonus,
	st.importoErogabile
FROM
	`iniz_22` st
	LEFT JOIN `agenzie` ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS `vw_iniz_21`;
CREATE VIEW `vw_iniz_21` AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.numeroPolizze,
	st.premiCapital,
	st.premiDimensione,
	st.premiCapitalComputabili,
	st.premiDimensioneComputabili,
	st.totPremi,
	st.totPremiComputabili,
	st.obiettivo,
	st.percentualeObiettivo,
	SIGN(TRUNCATE((st.percentualeObiettivo/100),0)) AS obiettivoOK,
	st.importoErogabile
FROM
	`iniz_21` st
	LEFT JOIN `agenzie` ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS `vw_iniz_9`;
CREATE VIEW `vw_iniz_9` AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.obiettivo,
	st.numeroPolizze,
	st.totPremiComputabili,
	st.percentualeObiettivo,
	SIGN(TRUNCATE((st.percentualeObiettivo/100),0)) AS obiettivoOK,
	st.importoErogabile
FROM
	`iniz_9` st
	LEFT JOIN `agenzie` ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS `vw_iniz_6`;
CREATE VIEW `vw_iniz_6` AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivo,
	st.numeroPolizze,
	st.totPremiComputabili,
	st.percentualeObiettivo,
	SIGN(TRUNCATE((st.percentualeObiettivo/100),0)) AS obiettivoOK,
	st.importoErogabile
FROM
	`iniz_6` st
	LEFT JOIN `agenzie` ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS `vw_iniz_5`;
CREATE VIEW `vw_iniz_5` AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivo,
	st.numeroPolizze,
	st.totPremiComputabili,
	st.percentualeObiettivo,
	SIGN(TRUNCATE((st.percentualeObiettivo/100),0)) AS obiettivoOK,
	st.importoErogabile,
	st.posizionePerform,
	st.posizioneDelta
FROM
	`iniz_5` st
	LEFT JOIN `agenzie` ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS `vw_iniz_2`;
CREATE VIEW `vw_iniz_2` AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivo,
	st.numeroPolizze,
	st.totPremiComputabili,
	st.percentualeObiettivo,
	SIGN(TRUNCATE((st.percentualeObiettivo/100),0)) AS obiettivoOK,
	st.importoErogabile,
	st.posizione
FROM
	`iniz_2` st
	LEFT JOIN `agenzie` ag ON ( st.agenzia_id = ag.id )
;
