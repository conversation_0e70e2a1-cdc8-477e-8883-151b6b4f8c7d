ALTER TABLE iniz_96_intermediari
	ADD premiTOTALI					DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0		AFTER codEsazione,
	ADD pezziTOTALI					SMALLINT UNSIGNED NOT NULL DEFAULT 0			AFTER premiTOTALI,
	ADD premiCompTOTALI				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0		AFTER pezziTOTALI
;

DROP VIEW IF EXISTS vw_iniz_96_intermediari;
CREATE VIEW vw_iniz_96_intermediari AS
SELECT
	i.agenzia_id,
	ag.area,
	ag.district,
	i.user_id,
	u.nome,
	u.cognome,
	u.codEsazione,
	i.premiTOTALI,
	i.pezziTOTALI,
	i.premiCompTOTALI,
	i.punti,
	i.createdAt
FROM
	iniz_96_intermediari i
	LEFT JOIN agenzie ag ON ( i.agenzia_id = ag.id )
	LEFT JOIN users u ON (i.user_id = u.id)
;

DROP VIEW IF EXISTS vw_iniz_96_polizze;
CREATE VIEW vw_iniz_96_polizze AS
SELECT
	p.*
FROM
	iniz_polizze p
	INNER JOIN iniz_96_intermediari u ON ( p.agenzia_id = u.agenzia_id AND p.codEsazione = u.codEsazione)
	WHERE p.iniziativa_id = 96
;
