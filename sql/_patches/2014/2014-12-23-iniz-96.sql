ALTER TABLE iniz_96_intermediari
	ADD obiettivoOK					TINYINT UNSIGNED NOT NULL DEFAULT 0		AFTER punti
;


DROP VIEW IF EXISTS vw_iniz_96_intermediari;
CREATE VIEW vw_iniz_96_intermediari AS
SELECT
	i.agenzia_id,
	ag.area,
	ag.district,
	i.user_id,
	u.nome,
	u.cognome,
	u.codEsazione,
	i.premiTOTALI,
	i.pezziTOTALI,
	i.premiCompTOTALI,
	i.punti,
	i.obiettivoOK,
	i.createdAt
FROM
	iniz_96_intermediari i
	LEFT JOIN agenzie ag ON ( i.agenzia_id = ag.id )
	LEFT JOIN users u ON (i.user_id = u.id)
;

DROP PROCEDURE IF EXISTS updateI96_set_status_intermediario;
DELIMITER //
CREATE PROCEDURE updateI96_set_status_intermediario (
	IN p_user_id		MEDIUMINT UNSIGNED
)
	BEGIN
		DECLARE C_INIZIATIVA_ID					INT DEFAULT 96;
		DECLARE done 							INT DEFAULT 0;
		DECLARE C_OBIETTIVO_PEZZI				MEDIUMINT UNSIGNED;
		DECLARE C_OBIETTIVO_PREMI				DECIMAL(12,2) UNSIGNED;

		DECLARE v_agenzia_id					CHAR(4);
		DECLARE v_codEsazione					VARCHAR(6);
		DECLARE v_pezziTOTALI					SMALLINT UNSIGNED;
		DECLARE v_premiTOTALI					DECIMAL(12,2) UNSIGNED;
		DECLARE v_premiCompTOTALI				DECIMAL(12,2) UNSIGNED;

		DECLARE v_punti							MEDIUMINT UNSIGNED;
		DECLARE v_obiettivoOK					TINYINT UNSIGNED;

		SET C_INIZIATIVA_ID = 96;
		SET C_OBIETTIVO_PEZZI = 8;
		SET C_OBIETTIVO_PREMI = 3000;

		SELECT agenzia_id, codEsazione INTO v_agenzia_id, v_codEsazione FROM iniz_96_intermediari WHERE user_id = p_user_id;

-- pezzi & premi
		SELECT IFNULL(SUM(premioAnnuo),0), IFNULL(SUM(premioComputabile),0) INTO v_premiTOTALI, v_premiCompTOTALI
		FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codEsazione = v_codEsazione;
		SELECT IFNULL(COUNT(*),0) INTO v_pezziTOTALI
		FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codEsazione = v_codEsazione;

-- points
		SELECT IFNULL(SUM(FLOOR(premioComputabile)),0) INTO v_punti
		FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codEsazione = v_codEsazione;

-- totali & status
		IF v_pezziTOTALI >= C_OBIETTIVO_PEZZI AND v_premiCompTOTALI >= C_OBIETTIVO_PREMI THEN
			SET v_obiettivoOK = 1;
		ELSE
			SET v_obiettivoOK = 0;
		END IF;

-- update tables
		UPDATE iniz_96_intermediari SET
			pezziTOTALI = v_pezziTOTALI, premiTOTALI = v_premiTOTALI, premiCompTOTALI = v_premiCompTOTALI,
			punti = v_punti, obiettivoOK = v_obiettivoOK
		WHERE user_id = p_user_id
		;
	END; //
DELIMITER ;
