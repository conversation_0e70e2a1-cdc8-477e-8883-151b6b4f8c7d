ALTER TABLE users
	DROP FOREIGN KEY fk_users_agenziesub,
	DROP INDEX fk_users_agenziesub,
	DROP subagenzia_id
;
DROP TABLE agenzie_sub;

DROP VIEW IF EXISTS `vw_Users-Intermediari`;
CREATE VIEW `vw_Users-Intermediari` AS
	SELECT
		u.id,
		'INT' AS type,
		u.active,
		u.nome,
		u.cognome,
		u.login,
		u.password,
		u.email,
		u.cellulare,
		u.updatedAt,
		u.agenzia_id	AS agenziaID,
		u.codEsazione	AS intermediarioID,
		u.rui AS RUI,
		if (u.nome is null,u.cognome,NULL) AS ragionesociale,
		u.piva AS PIVA,
		if (ruolo='COLL_AGZ',1,
			if (ruolo='DIP_AGZ',2,
				if (ruolo='SUBAGZ',3,
					if (ruolo='COLL_SUBAGZ',4,
						if (ruolo='DIP_SUBAGZ',5,ruolo
						))))) AS ruolo,
		u.agenzia_id	AS agCodice, -- verificare
		a.nome			AS agNome,
		a.localita		AS agLocalita,
		a.cap			AS agCap,
		g.sigla			AS agProvincia,
		a.telefono		AS agTelefono,
		a.email			AS agEmail

	FROM users u
		INNER JOIN agenzie a ON a.id = u.agenzia_id
		INNER JOIN geo_province g ON a.provincia_id = g.id
	WHERE type='INTERMEDIARIO'
;
