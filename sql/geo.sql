/* ======================= TABLES ======================================================= */

SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";

DROP TABLE IF EXISTS geo_comuni;
DROP TABLE IF EXISTS geo_province;
DROP TABLE IF EXISTS geo_regioni;

CREATE TABLE IF NOT EXISTS geo_regioni (
	id					tinyint unsigned not NULL,
	nome				varchar(25) not NULL,
	-- system data
	updatedAt			timestamp NOT NULL default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
	PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS geo_province (
	id					tinyint unsigned not NULL,
	regione_id			tinyint unsigned not NULL,
	nome				varchar(20) not NULL,
	sigla				char(2) not NULL,
	-- system data
	updatedAt			timestamp NOT NULL default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
	PRIMARY KEY (id),
	CONSTRAINT fk_geo_province FOREIGN KEY (regione_id) REFERENCES geo_regioni (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS geo_comuni (
	id					int unsigned not NULL,
	regione_id			tinyint unsigned not NULL,
	provincia_id		tinyint unsigned not NULL,
	comune_id			smallint unsigned not NULL,
	nome				varchar(20) not NULL,
	-- system data
	updatedAt			timestamp NOT NULL default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
	PRIMARY KEY (id),
	UNIQUE KEY uk_geo_comuni (regione_id, provincia_id, comune_id),
	CONSTRAINT fk_geo_comuni_1 FOREIGN KEY (regione_id) REFERENCES geo_regioni (id) ON DELETE CASCADE,
	CONSTRAINT fk_geo_comuni_2 FOREIGN KEY (provincia_id) REFERENCES geo_province (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS geo_aree (
	id					tinyint unsigned not NULL,
	codice				varchar(2) not NULL,
	nome				varchar(20) not NULL,
	status				ENUM('ON', 'OFF') NOT NULL DEFAULT 'ON',
	PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS geo_districts (
	id					smallint unsigned not NULL,
	area_id				tinyint unsigned not NULL,
	nome				varchar(30) not NULL,
	status				ENUM('ON', 'OFF') NOT NULL DEFAULT 'ON',
	PRIMARY KEY (id),
	CONSTRAINT fk_geo_districts FOREIGN KEY (area_id) REFERENCES geo_aree (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


SET FOREIGN_KEY_CHECKS=1;

/* ======================= TRIGGERS ======================================================= */

/* ======================= VIEWS =========================================== */

/* ======================= STORED PROCEDURES =========================================== */
