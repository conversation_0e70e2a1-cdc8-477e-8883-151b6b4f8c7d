-- ----------------------------------------------------------------------------- +
-- Author F<PERSON>
-- Patch db portaleagendo/formazione_1.0 -> portaleagendo/formazione_1.0.1

-- Aggiunge index, unique, foreign keys ed altri vincoli mancanti a portaleagendo.FOR*
-- ed effettua le relative normalizzazioni nei valori delle tabelle.
-- Molte delle colonne che ora implementano una FK diventano nullabili:
-- tutte quelle che prima della patch prevedevano il valore '0', ora prevedono NULL.

-- Nella maggioranza dei casi i riferimenti ad users del tipo editedby, createdby, 
-- updatedby, etc. sono stati implementati con delle FK 'ON DELETE SET NULL' per 
-- gestire eventuali cancellazioni nella tabella users.

-- @todo: verificare tutti i check x == 0 nel codice
-- ----------------------------------------------------------------------------- +

-- ----------------------------------------------------------------------------- +
-- FORCorsi
-- ----------------------------------------------------------------------------- +
ALTER TABLE  `FORCorsi` CHANGE  `createdby`  `createdby` MEDIUMINT( 8 ) UNSIGNED NOT NULL;
ALTER TABLE  `FORCorsi` CHANGE  `editedby`  `editedby` MEDIUMINT( 8 ) UNSIGNED NOT NULL;
ALTER TABLE  `FORCorsi` ADD INDEX (  `createdby` );
ALTER TABLE  `FORCorsi` ADD INDEX (  `editedby`  );

-- ----------------------------------------------------------------------------- +
-- FORCorsi.dependon
-- ----------------------------------------------------------------------------- +
ALTER TABLE  `FORCorsi` CHANGE  `dependon`  `dependon` MEDIUMINT( 8 ) UNSIGNED NULL DEFAULT NULL;
UPDATE `FORCorsi` SET `dependon` = NULL WHERE `dependon` = 0;
ALTER TABLE  `FORCorsi` ADD INDEX (  `dependon` );

ALTER TABLE  `FORCorsi` 
  ADD CONSTRAINT `fk_forcorsi_createdby` FOREIGN KEY (  `createdby` ) REFERENCES  `users` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_forcorsi_editedby` FOREIGN KEY (  `editedby` ) REFERENCES  `users` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_forcorsi_dependon` FOREIGN KEY (  `dependon` ) REFERENCES  `FORCorsi` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION ;

-- ----------------------------------------------------------------------------- +
-- FORAttestato
-- ----------------------------------------------------------------------------- +
ALTER TABLE  `FORAttestato` CHANGE  `idcorso`  `idcorso` MEDIUMINT( 8 ) UNSIGNED NOT NULL;
ALTER TABLE  `FORAttestato` CHANGE  `createdby`  `createdby` MEDIUMINT( 8 ) UNSIGNED NULL;
ALTER TABLE  `FORAttestato` CHANGE  `editedby`  `editedby` MEDIUMINT( 8 ) UNSIGNED NULL;
ALTER TABLE  `FORAttestato` ADD INDEX (  `idcorso` );
ALTER TABLE  `FORAttestato` ADD INDEX (  `createdby` );
ALTER TABLE  `FORAttestato` ADD INDEX (  `editedby` );
UPDATE `FORAttestato` SET `editedby` = NULL WHERE editedby = 0;
ALTER TABLE `FORAttestato`
  ADD CONSTRAINT `fk_forattestato_corso` FOREIGN KEY (`idcorso`) REFERENCES `FORCorsi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_forattestato_createdby` FOREIGN KEY (`createdby`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_forattestato_editedby` FOREIGN KEY (`editedby`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION;

-- ----------------------------------------------------------------------------- +
-- FORDescrizione
-- ----------------------------------------------------------------------------- +
ALTER TABLE `FORDescrizione` CHANGE `createdby` `createdby` MEDIUMINT( 8 ) UNSIGNED NULL ;
ALTER TABLE `FORDescrizione` CHANGE `editedby` `editedby` MEDIUMINT( 8 ) UNSIGNED NULL DEFAULT NULL;
ALTER TABLE `FORDescrizione` CHANGE `idcorso` `idcorso` MEDIUMINT( 8 ) UNSIGNED NOT NULL ;
ALTER TABLE `FORDescrizione` ADD INDEX ( `createdby` );
ALTER TABLE `FORDescrizione` ADD INDEX ( `editedby` );
ALTER TABLE `FORDescrizione` ADD INDEX ( `idcorso` );
UPDATE `FORDescrizione` SET `createdby` = NULL WHERE `createdby` = 0;
UPDATE `FORDescrizione` SET `editedby` = NULL WHERE `editedby` = 0;
ALTER TABLE `FORDescrizione` ADD CONSTRAINT `fk_fordescrizione_createdby` FOREIGN KEY ( `createdby` ) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION;
ALTER TABLE `FORDescrizione` ADD CONSTRAINT `fk_fordescrizione_editedby` FOREIGN KEY ( `editedby` ) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION;
ALTER TABLE `FORDescrizione` ADD CONSTRAINT `fk_fordescrizione_idcorso` FOREIGN KEY ( `idcorso` ) REFERENCES `FORCorsi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------------------------------------------------------- +
-- Patch on FORObiettivi
-- ----------------------------------------------------------------------------- +
ALTER TABLE `FORObiettivi` CHANGE `idcorso` `idcorso` MEDIUMINT( 8 ) UNSIGNED NOT NULL;
ALTER TABLE `FORObiettivi` CHANGE `createdby` `createdby` MEDIUMINT( 8 ) UNSIGNED NULL;
ALTER TABLE `FORObiettivi` CHANGE `editedby` `editedby` MEDIUMINT( 8 ) UNSIGNED NULL DEFAULT NULL;
ALTER TABLE `FORObiettivi` ADD INDEX ( `idcorso` );
ALTER TABLE `FORObiettivi` ADD INDEX ( `createdby` );
ALTER TABLE `FORObiettivi` ADD INDEX ( `editedby` );
UPDATE `FORObiettivi` SET `editedby` = NULL WHERE `editedby` = 0;
ALTER TABLE `FORObiettivi` ADD CONSTRAINT `fk_forobiettivi_idcorso` FOREIGN KEY ( `idcorso` ) REFERENCES `FORCorsi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION;
ALTER TABLE `FORObiettivi` ADD CONSTRAINT `fk_forobiettivi_createdby` FOREIGN KEY ( `createdby` ) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION;
ALTER TABLE `FORObiettivi` ADD CONSTRAINT `fk_forobiettivi_editedby` FOREIGN KEY ( `editedby` ) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION;

-- ----------------------------------------------------------------------------- +
-- Patch on FORcorso2files
-- ----------------------------------------------------------------------------- +
ALTER TABLE `FORcorso2files` CHANGE `idcorso` `idcorso` MEDIUMINT( 8 ) UNSIGNED NOT NULL;
ALTER TABLE `FORcorso2files` CHANGE `uploadedby` `uploadedby` MEDIUMINT( 8 ) UNSIGNED NULL DEFAULT NULL;
ALTER TABLE `FORcorso2files` ADD INDEX ( `idcorso` );
ALTER TABLE `FORcorso2files` ADD INDEX ( `uploadedby` );
UPDATE `FORcorso2files` SET `uploadedby` = NULL WHERE `uploadedby` = 0;
ALTER TABLE `FORcorso2files` ADD CONSTRAINT `fk_forcorso2files_idcorso` FOREIGN KEY ( `idcorso` ) REFERENCES `FORCorsi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION;
ALTER TABLE `FORcorso2files` ADD CONSTRAINT `fk_forcorso2files_uploadedby` FOREIGN KEY ( `uploadedby` ) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION;

-- ----------------------------------------------------------------------------- +
-- Patch on FORcorso2webfiles
-- ----------------------------------------------------------------------------- +
ALTER TABLE `FORcorso2webfiles` CHANGE `idcorso` `idcorso` MEDIUMINT( 8 ) UNSIGNED NOT NULL;
ALTER TABLE `FORcorso2webfiles` CHANGE `uploadedby` `uploadedby` MEDIUMINT( 8 ) UNSIGNED NULL DEFAULT NULL;
ALTER TABLE `FORcorso2webfiles` ADD INDEX ( `idcorso` );
ALTER TABLE `FORcorso2webfiles` ADD INDEX ( `uploadedby` );
UPDATE `FORcorso2webfiles` SET `uploadedby` = NULL WHERE `uploadedby` = 0;
ALTER TABLE `FORcorso2webfiles` ADD CONSTRAINT `fk_forcorso2webfiles_idcorso` FOREIGN KEY ( `idcorso` ) REFERENCES `FORCorsi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION;
ALTER TABLE `FORcorso2webfiles` ADD CONSTRAINT `fk_forcorso2webfiles_uploadedby` FOREIGN KEY ( `uploadedby` ) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION;

-- ----------------------------------------------------------------------------- +
-- Patch on FORProgramma
-- ----------------------------------------------------------------------------- +
ALTER TABLE `FORProgramma` CHANGE `idcorso` `idcorso` MEDIUMINT( 8 ) UNSIGNED NOT NULL;
ALTER TABLE `FORProgramma` CHANGE `createdby` `createdby` MEDIUMINT( 8 ) UNSIGNED NULL DEFAULT NULL;
ALTER TABLE `FORProgramma` ADD INDEX ( `idcorso` );
ALTER TABLE `FORProgramma` ADD INDEX ( `createdby` );
UPDATE `FORProgramma` SET `createdby` = NULL WHERE `createdby` = 0;
ALTER TABLE `FORProgramma` ADD CONSTRAINT `fk_forprogramma_idcorso` FOREIGN KEY ( `idcorso` ) REFERENCES `FORCorsi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION;
ALTER TABLE `FORProgramma` ADD CONSTRAINT `fk_forprogramma_createdby` FOREIGN KEY ( `createdby` ) REFERENCES `users` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------------------------------------------------------- +
-- FORStrutture
-- ----------------------------------------------------------------------------- +
ALTER TABLE `FORStrutture` CHANGE  `id`  `id` MEDIUMINT( 8 ) UNSIGNED NOT NULL AUTO_INCREMENT;
ALTER TABLE `FORStrutture` CHANGE `created` `created` MEDIUMINT( 8 ) UNSIGNED NULL;
ALTER TABLE `FORStrutture` CHANGE `editedby` `editedby` MEDIUMINT( 8 ) UNSIGNED NULL DEFAULT NULL;
-- @fixme: c'è stato un incasinamento durante la nomenclatura a quanto pare.
-- in questa tabella created è l'id utente, createdby è il timestamp... illogicamente, e diversamente da tutte le altre tabelle icontrate fino ad ora.
ALTER TABLE  `FORStrutture` ADD INDEX (  `created` );
ALTER TABLE  `FORStrutture` ADD INDEX (  `editedby` );
UPDATE `FORStrutture` SET `editedby` = NULL WHERE `editedby` = 0;
ALTER TABLE `FORStrutture` ADD CONSTRAINT `fk_forstrutture_created` FOREIGN KEY ( `created` ) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION;
ALTER TABLE `FORStrutture` ADD CONSTRAINT `fk_forstrutture_editedby` FOREIGN KEY ( `editedby` ) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION;

-- ----------------------------------------------------------------------------- +
-- FORSedi
-- ----------------------------------------------------------------------------- +
ALTER TABLE  `FORSedi` CHANGE  `id`  `id` MEDIUMINT( 8 ) UNSIGNED NOT NULL AUTO_INCREMENT;
ALTER TABLE `FORSedi` CHANGE `sede` `sede` MEDIUMINT( 8 ) UNSIGNED NOT NULL;
ALTER TABLE `FORSedi` CHANGE `corso` `corso` MEDIUMINT( 8 ) UNSIGNED NOT NULL;
ALTER TABLE `FORSedi` CHANGE `createdby` `createdby` MEDIUMINT( 8 ) UNSIGNED NULL;
ALTER TABLE `FORSedi` CHANGE `updateby` `updateby` MEDIUMINT( 8 ) UNSIGNED NULL DEFAULT NULL;
ALTER TABLE `FORSedi` ADD INDEX (  `sede` );
ALTER TABLE `FORSedi` ADD INDEX (  `corso` );
ALTER TABLE `FORSedi` ADD INDEX (  `createdby` );
ALTER TABLE `FORSedi` ADD INDEX (  `updateby` );
UPDATE `FORSedi` SET `updateby` = NULL WHERE `updateby` = 0;
-- no action on delete: voglio evitare che una SEDE di corso sia eliminata se si cancella una STRUTTURA. In particolare voglio che non sia possibile
-- eliminare una struttura linkata ad una sede.
ALTER TABLE `FORSedi` ADD CONSTRAINT `fk_forsedi_sede` FOREIGN KEY ( `sede` ) REFERENCES `FORStrutture` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;
-- delete cascade: elimino la sede se il corso è stato eliminato.
ALTER TABLE `FORSedi` ADD CONSTRAINT `fk_forsedi_corso` FOREIGN KEY ( `corso` ) REFERENCES `FORCorsi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION;
ALTER TABLE `FORSedi` ADD CONSTRAINT `fk_forsedi_createdby` FOREIGN KEY ( `createdby` ) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION;
ALTER TABLE `FORSedi` ADD CONSTRAINT `fk_forsedi_updateby` FOREIGN KEY ( `updateby` ) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION;

-- ----------------------------------------------------------------------------- +
-- FORUser-other
-- @fixme questa colonna dovrebbe diventare la chiave primaria.
-- ----------------------------------------------------------------------------- +
ALTER TABLE `FORUser-other` CHANGE `iduser` `iduser` MEDIUMINT( 8 ) UNSIGNED NOT NULL;
ALTER TABLE `FORUser-other` ADD UNIQUE (  `iduser` );
ALTER TABLE `FORUser-other` ADD CONSTRAINT `fk_foruserother_user` FOREIGN KEY ( `iduser` ) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------------------------------------------------------- +
-- FORSede2uditori
-- ----------------------------------------------------------------------------- +
ALTER TABLE  `FORSede2uditori` CHANGE  `id`  `id` MEDIUMINT( 8 ) UNSIGNED NOT NULL AUTO_INCREMENT;
ALTER TABLE  `FORSede2uditori` CHANGE  `idcorso`  `idcorso` MEDIUMINT( 8 ) UNSIGNED NOT NULL;
ALTER TABLE  `FORSede2uditori` CHANGE  `idsede`  `idsede` MEDIUMINT( 8 ) UNSIGNED NOT NULL;
ALTER TABLE  `FORSede2uditori` CHANGE  `iduditori`  `iduditori` MEDIUMINT( 8 ) UNSIGNED NOT NULL;
ALTER TABLE `FORSede2uditori` ADD INDEX (  `idcorso` );
ALTER TABLE `FORSede2uditori` ADD INDEX (  `idsede` );
ALTER TABLE `FORSede2uditori` ADD INDEX (  `iduditori` );
ALTER TABLE  `FORSede2uditori` ADD CONSTRAINT `fk_forsedetouditori_corso` FOREIGN KEY (  `idcorso` ) REFERENCES  `FORCorsi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION ;
ALTER TABLE  `FORSede2uditori` ADD CONSTRAINT `fk_forsedetouditori_sede` FOREIGN KEY (  `idsede` ) REFERENCES  `FORSedi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION ;
ALTER TABLE  `FORSede2uditori` ADD CONSTRAINT `fk_forsedetouditori_uditore` FOREIGN KEY (  `iduditori` ) REFERENCES  `users` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION ;

-- ----------------------------------------------------------------------------- +
-- FORSede2formazione
-- ----------------------------------------------------------------------------- +
ALTER TABLE  `FORSede2formazione` CHANGE  `id`  `id` MEDIUMINT( 8 ) UNSIGNED NOT NULL AUTO_INCREMENT;
ALTER TABLE  `FORSede2formazione` CHANGE  `idcorso`  `idcorso` MEDIUMINT( 8 ) UNSIGNED NOT NULL;
ALTER TABLE  `FORSede2formazione` CHANGE  `idsede`  `idsede` MEDIUMINT( 8 ) UNSIGNED NOT NULL;
ALTER TABLE  `FORSede2formazione` CHANGE  `idformazione`  `idformazione` MEDIUMINT( 8 ) UNSIGNED NOT NULL;
ALTER TABLE `FORSede2formazione` ADD INDEX (  `idcorso` );
ALTER TABLE `FORSede2formazione` ADD INDEX (  `idsede` );
ALTER TABLE `FORSede2formazione` ADD INDEX (  `idformazione` );
ALTER TABLE  `FORSede2formazione` ADD CONSTRAINT `fk_forsedetoformazione_corso` FOREIGN KEY (  `idcorso` ) REFERENCES  `FORCorsi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION ;
ALTER TABLE  `FORSede2formazione` ADD CONSTRAINT `fk_forsedetoformazione_sede` FOREIGN KEY (  `idsede` ) REFERENCES  `FORSedi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION ;
ALTER TABLE  `FORSede2formazione` ADD CONSTRAINT `fk_forsedetoformazione_formazione` FOREIGN KEY (  `idformazione` ) REFERENCES  `users` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION ;

-- ----------------------------------------------------------------------------- +
-- FORSede2formatori
-- ----------------------------------------------------------------------------- +
ALTER TABLE  `FORSede2formatori` CHANGE  `id`  `id` MEDIUMINT( 8 ) UNSIGNED NOT NULL AUTO_INCREMENT;
ALTER TABLE  `FORSede2formatori` CHANGE  `idcorso`  `idcorso` MEDIUMINT( 8 ) UNSIGNED NOT NULL;
ALTER TABLE  `FORSede2formatori` CHANGE  `idsede`  `idsede` MEDIUMINT( 8 ) UNSIGNED NOT NULL;
ALTER TABLE  `FORSede2formatori` CHANGE  `idformatore`  `idformatore` MEDIUMINT( 8 ) UNSIGNED NOT NULL;
ALTER TABLE `FORSede2formatori` ADD INDEX (  `idcorso` );
ALTER TABLE `FORSede2formatori` ADD INDEX (  `idsede` );
ALTER TABLE `FORSede2formatori` ADD INDEX (  `idformatore` );
ALTER TABLE  `FORSede2formatori` ADD CONSTRAINT `fk_forsedetoformatori_corso` FOREIGN KEY (  `idcorso` ) REFERENCES  `FORCorsi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION ;
ALTER TABLE  `FORSede2formatori` ADD CONSTRAINT `fk_forsedetoformatori_sede` FOREIGN KEY (  `idsede` ) REFERENCES  `FORSedi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION ;
ALTER TABLE  `FORSede2formatori` ADD CONSTRAINT `fk_forsedetoformatori_formatore` FOREIGN KEY (  `idformatore` ) REFERENCES  `users` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION ;

-- ----------------------------------------------------------------------------- +
-- FORiscrizioni
-- ----------------------------------------------------------------------------- +
ALTER TABLE `FORiscrizioni` CHANGE  `idcorso`  `idcorso` MEDIUMINT( 8 ) UNSIGNED NOT NULL;
ALTER TABLE `FORiscrizioni` CHANGE `bookedby` `bookedby` MEDIUMINT( 8 ) UNSIGNED NULL;
ALTER TABLE `FORiscrizioni` CHANGE  `sede`  `sede` MEDIUMINT( 8 ) UNSIGNED NULL;
ALTER TABLE `FORiscrizioni` CHANGE `createdby` `createdby` MEDIUMINT(8) UNSIGNED NULL;
ALTER TABLE `FORiscrizioni` CHANGE `updateby` `updateby` MEDIUMINT( 8 ) UNSIGNED NULL DEFAULT NULL;
ALTER TABLE `FORiscrizioni` CHANGE `groupamavaliduser` `groupamavaliduser` MEDIUMINT( 8 ) UNSIGNED NULL DEFAULT NULL;
UPDATE `FORiscrizioni` SET `updateby` = NULL WHERE `updateby` = 0;
UPDATE `FORiscrizioni` SET `bookedby` = NULL WHERE `bookedby` = 0;
-- @fixme se ho dovuto lanciare questa query significa che in qualche caso l'iscrizione viene scritta senza "createdby". Se non 
-- fixo il codice devo per forza prevedere il default NULL nella colonna. Verificare.
UPDATE `FORiscrizioni` SET `createdby` = NULL WHERE `createdby` = 0;
-- @fix molte righe avevano il valore della colonna 'sede' che non corrispondeva ad alcuna riga nella colonna FORSedi.
-- Si è scelto di intervenire impostando tutti questi riferimenti a NULL
UPDATE `FORiscrizioni` SET `sede` = NULL WHERE `sede` = 0;
UPDATE `FORiscrizioni` i set i.sede = NULL WHERE i.sede not in (select id from `FORSedi`);
UPDATE `FORiscrizioni` SET `groupamavaliduser` = NULL WHERE `groupamavaliduser` = 0;
ALTER TABLE `FORiscrizioni` ADD INDEX (  `idcorso` );
ALTER TABLE `FORiscrizioni` ADD INDEX (  `bookedby` );
ALTER TABLE `FORiscrizioni` ADD INDEX (  `sede` );
ALTER TABLE `FORiscrizioni` ADD INDEX (  `createdby` );
ALTER TABLE `FORiscrizioni` ADD INDEX (  `updateby` );
ALTER TABLE `FORiscrizioni` ADD INDEX (  `groupamavaliduser` );
ALTER TABLE  `FORiscrizioni` ADD CONSTRAINT `fk_foriscrizioni_idcorso` FOREIGN KEY (  `idcorso` ) REFERENCES  `FORCorsi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION ;
ALTER TABLE  `FORiscrizioni` ADD CONSTRAINT `fk_foriscrizioni_bookedby` FOREIGN KEY (  `bookedby` ) REFERENCES  `users` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION ;
ALTER TABLE  `FORiscrizioni` ADD CONSTRAINT `fk_foriscrizioni_createdby` FOREIGN KEY (  `createdby` ) REFERENCES  `users` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION ;
ALTER TABLE  `FORiscrizioni` ADD CONSTRAINT `fk_foriscrizioni_updateby` FOREIGN KEY (  `updateby` ) REFERENCES  `users` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION ;
ALTER TABLE  `FORiscrizioni` ADD CONSTRAINT `fk_foriscrizioni_sede` FOREIGN KEY (  `sede` ) REFERENCES  `FORSedi` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION ;
ALTER TABLE  `FORiscrizioni` ADD CONSTRAINT `fk_foriscrizioni_groupamavaliduser` FOREIGN KEY (  `groupamavaliduser` ) REFERENCES  `users` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION ;

-- ----------------------------------------------------------------------------- +
-- FORcorso2aree
-- ----------------------------------------------------------------------------- +
ALTER TABLE  `FORcorso2aree` CHANGE  `idcorso`  `idcorso` MEDIUMINT( 8 ) UNSIGNED NOT NULL;
ALTER TABLE  `FORcorso2aree` ADD INDEX (  `idcorso` );
ALTER TABLE  `FORcorso2aree` ADD CONSTRAINT `fk_forcorsoaree_idcorso` FOREIGN KEY (  `idcorso` ) REFERENCES  `FORCorsi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION ;

-- ----------------------------------------------------------------------------- +
-- FORcorso2distict
-- ----------------------------------------------------------------------------- +
ALTER TABLE  `FORcorso2distict` CHANGE  `idcorso`  `idcorso` MEDIUMINT( 8 ) UNSIGNED NOT NULL;
ALTER TABLE  `FORcorso2distict` ADD INDEX (  `idcorso` );
ALTER TABLE  `FORcorso2distict` ADD CONSTRAINT `fk_forcorsodist_idcorso` FOREIGN KEY (  `idcorso` ) REFERENCES  `FORCorsi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION ;

-- ----------------------------------------------------------------------------- +
-- FORSedi2date
-- ----------------------------------------------------------------------------- +
ALTER TABLE  `FORSedi2date` CHANGE  `id`  `id` MEDIUMINT( 8 ) UNSIGNED NOT NULL;
ALTER TABLE  `FORSedi2date` CHANGE  `corso`  `corso` MEDIUMINT( 8 ) UNSIGNED NOT NULL;
ALTER TABLE  `FORSedi2date` CHANGE  `sede`  `sede` MEDIUMINT( 8 ) UNSIGNED NOT NULL;
ALTER TABLE  `FORSedi2date` ADD INDEX (  `corso` );
ALTER TABLE  `FORSedi2date` ADD INDEX (  `sede` );
ALTER TABLE  `FORSedi2date` ADD CONSTRAINT `fk_forsedidate_corso` FOREIGN KEY (  `corso` ) REFERENCES  `FORCorsi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION ;
ALTER TABLE  `FORSedi2date` ADD CONSTRAINT `fk_forsedidate_sede` FOREIGN KEY (  `sede` ) REFERENCES  `FORSedi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION ;
