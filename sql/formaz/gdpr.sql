CREATE TABLE `formaz_gdpr` (
  `user_id` mediumint(8) UNSIGNED NOT NULL,
  `title` varchar(512) NOT NULL,
  `structure` varchar(512) NOT NULL,
  `date` date NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


ALTER TABLE `formaz_gdpr`
ADD PRIMARY KEY (`user_id`),
ADD KEY `user_id` (`user_id`);

ALTER TABLE `formaz_gdpr`
ADD CONSTRAINT `formaz_gdpr_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);

