-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version *******
-- http://www.phpmyadmin.net
--
-- Host: localhost
-- Generato il: Nov 16, 2012 alle 22:00
-- Versione del server: 5.1.63
-- Versione PHP: 5.3.2-1ubuntu4.17

SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;

--
-- Database: `portaleagendo`
--

-- --------------------------------------------------------

--
-- Strutt<PERSON> della tabella `FORAttestato`
--

DROP TABLE IF EXISTS `FORAttestato`;
CREATE TABLE IF NOT EXISTS `FORAttestato` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `idcorso` mediumint(8) unsigned NOT NULL,
  `attestato` text NOT NULL,
  `createdby` mediumint(8) unsigned DEFAULT NULL,
  `created` datetime NOT NULL,
  `editedby` mediumint(8) unsigned DEFAULT NULL,
  `edited` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idcorso` (`idcorso`),
  KEY `createdby` (`createdby`),
  KEY `editedby` (`editedby`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Struttura della tabella `FORCorsi`
--

DROP TABLE IF EXISTS `FORCorsi`;
CREATE TABLE IF NOT EXISTS `FORCorsi` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `type` enum('STANDARD','TEATROIMPRESA') NOT NULL DEFAULT 'STANDARD',
  `nomecorso` varchar(255) NOT NULL,
  `dettagli` text NOT NULL,
  `duratagiorni` int(11) NOT NULL,
  `oreformative` DECIMAL(5,2) NOT NULL,
  `dependon` mediumint(8) unsigned DEFAULT NULL,
  `agenti` enum('0','1') NOT NULL,
  `intermediari` enum('0','1') NOT NULL,
  `dipendenti` enum('0','1') NOT NULL,
  `dipendenti_no_rui` enum('0','1') NOT NULL,
  `stato` enum('on','off','ann','arc','anc') NOT NULL,
  `web` enum('0','1') NOT NULL,
  `createdby` mediumint(8) unsigned NOT NULL,
  `created` datetime NOT NULL,
  `editedby` mediumint(8) unsigned NOT NULL,
  `edited` datetime NOT NULL,
  `csv` enum('0','1') NOT NULL,
  `protocolloFormazione` varchar(128) DEFAULT NULL,
  `limiteAgenzia` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `createdby` (`createdby`),
  KEY `editedby` (`editedby`),
  KEY `dependon` (`dependon`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Struttura della tabella `FORcorso2aree`
--

DROP TABLE IF EXISTS `FORcorso2aree`;
CREATE TABLE IF NOT EXISTS `FORcorso2aree` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `idcorso` mediumint(8) unsigned NOT NULL,
  `idarea` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idcorso` (`idcorso`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Struttura della tabella `FORcorso2distict`
--

DROP TABLE IF EXISTS `FORcorso2distict`;
CREATE TABLE IF NOT EXISTS `FORcorso2distict` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `idcorso` mediumint(8) unsigned NOT NULL,
  `iddistrict` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idcorso` (`idcorso`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Struttura della tabella `FORcorso2files`
--

DROP TABLE IF EXISTS `FORcorso2files`;
CREATE TABLE IF NOT EXISTS `FORcorso2files` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `idcorso` mediumint(8) unsigned NOT NULL,
  `file` varchar(255) NOT NULL,
  `type` enum('pdf','ppt','doc','zip','txt','xls','img') NOT NULL,
  `ext` varchar(10) NOT NULL,
  `uploadedby` mediumint(8) unsigned DEFAULT NULL,
  `when` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idcorso` (`idcorso`),
  KEY `uploadedby` (`uploadedby`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Struttura della tabella `FORcorso2webfiles`
--

DROP TABLE IF EXISTS `FORcorso2webfiles`;
CREATE TABLE IF NOT EXISTS `FORcorso2webfiles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `idcorso` mediumint(8) unsigned NOT NULL,
  `pubblico` BOOLEAN NOT NULL DEFAULT 0,
  `file` varchar(255) NOT NULL,
  `type` varchar(20) NOT NULL,
  `ext` varchar(20) NOT NULL,
  `uploadedby` mediumint(8) unsigned DEFAULT NULL,
  `when` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idcorso` (`idcorso`),
  KEY `uploadedby` (`uploadedby`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Struttura della tabella `FORDescrizione`
--

DROP TABLE IF EXISTS `FORDescrizione`;
CREATE TABLE IF NOT EXISTS `FORDescrizione` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `idcorso` mediumint(8) unsigned NOT NULL,
  `descrizione` text NOT NULL,
  `createdby` mediumint(8) unsigned DEFAULT NULL,
  `created` datetime NOT NULL,
  `editedby` mediumint(8) unsigned DEFAULT NULL,
  `edited` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `createdby` (`createdby`),
  KEY `editedby` (`editedby`),
  KEY `idcorso` (`idcorso`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Struttura della tabella `FORiscrizioni`
--

DROP TABLE IF EXISTS `FORiscrizioni`;
CREATE TABLE IF NOT EXISTS `FORiscrizioni` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `iduser` mediumint(8) unsigned NOT NULL,
  `tipo` enum('AGT','INT','DIP') NOT NULL,
  `agenzia` varchar(15) NOT NULL,
  `idcorso` mediumint(8) unsigned NOT NULL,
  `booked` datetime NOT NULL,
  `bookedvia` enum('web','pdf','admin','none') NOT NULL,
  `bookedby` mediumint(8) unsigned DEFAULT NULL,
  `sede` mediumint(8) unsigned DEFAULT NULL,
  `groupamavalid` enum('w','s','n') NOT NULL,
  `groupamavalidation` datetime NOT NULL,
  `groupamavaliduser` mediumint(8) unsigned DEFAULT NULL,
  `iscritto` enum('0','1','deleted') NOT NULL,
  `iscrittowhen` datetime NOT NULL,
  `presenze` varchar(255) NOT NULL,
  `esito` enum('none','ok','ko') NOT NULL,
  `questionario` enum('0','1') NOT NULL,
  `createdby` mediumint(8) unsigned DEFAULT NULL,
  `created` datetime NOT NULL,
  `updateby` mediumint(8) unsigned DEFAULT NULL,
  `updateat` datetime NOT NULL,
  `promemoria` enum('0','1') NOT NULL,
  `comunicazione` enum('0','1') NOT NULL,
  `oreEffettive` DECIMAL(5,2) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_corso` (`iduser`,`idcorso`),
  KEY `idcorso` (`idcorso`),
  KEY `bookedby` (`bookedby`),
  KEY `sede` (`sede`),
  KEY `createdby` (`createdby`),
  KEY `updateby` (`updateby`),
  KEY `groupamavaliduser` (`groupamavaliduser`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Struttura della tabella `formaz_corso`
--

DROP TABLE IF EXISTS `formaz_corso`;
CREATE TABLE IF NOT EXISTS `formaz_corso` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `anno` year(4) NOT NULL,
  `titolo` varchar(128) NOT NULL,
  `tipo` enum('NORM','TECN','FISC','ECON','SSPR','ALTRO','GIUR','TECA','ADMG','INFO','ACOP') NOT NULL,
  `erogato` enum('DIREZ','AGENTI','BANCHE','INTFNZ','SIM','POSTEIT','SOCFOR','ALTRO') NOT NULL,
  `modalita` enum('AULA','DIST') NOT NULL,
  `protocollo` varchar(9) DEFAULT NULL,
  `oreFormative` DECIMAL(5,2) unsigned NOT NULL,
  `status` enum('ON','OFF','ANN','ARC') NOT NULL DEFAULT 'OFF',
  `impl` varchar(32) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Struttura della tabella `formaz_corso_area`
--

DROP TABLE IF EXISTS `formaz_corso_area`;
CREATE TABLE IF NOT EXISTS `formaz_corso_area` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `corso_id` mediumint(8) unsigned NOT NULL,
  `area` int(11) NOT NULL,
  `oreFormative` DECIMAL(5,2) unsigned NOT NULL,
  `protocollo` varchar(9) NOT NULL,
  `sede` varchar(128) NOT NULL,
  `data` datetime NOT NULL,
  `dataSecondoGiorno` datetime DEFAULT NULL,
  `formatore` varchar(128) NOT NULL,
  `status` enum('ON','OFF','ANN','ARC') NOT NULL,
  PRIMARY KEY (`id`),
  KEY `corso_id` (`corso_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Struttura della tabella `formaz_corso_elearning`
--

DROP TABLE IF EXISTS `formaz_corso_elearning`;
CREATE TABLE IF NOT EXISTS `formaz_corso_elearning` (
  `corso_id` mediumint(8) unsigned NOT NULL,
  `idRemoto` varchar(31) NOT NULL,
  PRIMARY KEY (`corso_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Struttura della tabella `formaz_elearning_fetch_session`
--

DROP TABLE IF EXISTS `formaz_elearning_fetch_session`;
CREATE TABLE IF NOT EXISTS `formaz_elearning_fetch_session` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `date` datetime NOT NULL,
  `dateInterval0` datetime NOT NULL,
  `dateInterval1` datetime NOT NULL,
  `count` mediumint(9) NOT NULL,
  `success` tinyint(1) NOT NULL,
  `message` varchar(127) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Struttura della tabella `formaz_emails`
--

DROP TABLE IF EXISTS `formaz_emails`;
CREATE TABLE IF NOT EXISTS `formaz_emails` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `type` enum('1-COMUNICAZIONE','2-PRENOTAZIONE','3-ISCRIZIONE','4-PROMEMORIA') NOT NULL,
  `corso_id` mediumint(8) unsigned NOT NULL,
  `subject` varchar(255) NOT NULL,
  `body` text NOT NULL,
  `attachment` varchar(255) DEFAULT NULL,
  `sendAt` timestamp NULL DEFAULT NULL,
  `sendBeforeDays` smallint(5) unsigned DEFAULT NULL,
  `isSent` tinyint(1) NOT NULL DEFAULT '0',
  `createdBy` mediumint(8) unsigned DEFAULT NULL,
  `createdAt` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `updatedBy` mediumint(8) unsigned DEFAULT NULL,
  `updatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_type_corso` (`type`,`corso_id`),
  KEY `fk_corso` (`corso_id`),
  KEY `fk_createdBy` (`createdBy`),
  KEY `fk_updatedBy` (`updatedBy`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Struttura della tabella `formaz_partecipazione`
--

DROP TABLE IF EXISTS `formaz_partecipazione`;
CREATE TABLE IF NOT EXISTS `formaz_partecipazione` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `corso_id` mediumint(8) unsigned NOT NULL,
  `user_id` mediumint(8) unsigned NOT NULL,
  `esito` enum('none','ok','ko') NOT NULL,
  `oreFormative` DECIMAL(5,2) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `corso_user` (`corso_id`,`user_id`),
  KEY `corso_id` (`corso_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Struttura della tabella `formaz_partecipazione_area`
--

DROP TABLE IF EXISTS `formaz_partecipazione_area`;
CREATE TABLE IF NOT EXISTS `formaz_partecipazione_area` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `partecipazione_id` mediumint(8) unsigned NOT NULL,
  `corso_area_id` mediumint(8) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `corso_area_id` (`corso_area_id`),
  KEY `partecipazione_id` (`partecipazione_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Struttura della tabella `formaz_partecipazione_elearning`
--

DROP TABLE IF EXISTS `formaz_partecipazione_elearning`;
CREATE TABLE IF NOT EXISTS `formaz_partecipazione_elearning` (
  `partecipazione_id` mediumint(8) unsigned NOT NULL,
  `corso_elearning_id` mediumint(8) unsigned NOT NULL,
  `punteggio` tinyint(1) unsigned NOT NULL,
  `dataCompletamento` datetime NOT NULL,
  PRIMARY KEY (`partecipazione_id`),
  KEY `corso_elearning_id` (`corso_elearning_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Struttura della tabella `FORObiettivi`
--

DROP TABLE IF EXISTS `FORObiettivi`;
CREATE TABLE IF NOT EXISTS `FORObiettivi` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `idcorso` mediumint(8) unsigned NOT NULL,
  `obiettivi` text NOT NULL,
  `createdby` mediumint(8) unsigned DEFAULT NULL,
  `created` datetime NOT NULL,
  `editedby` mediumint(8) unsigned DEFAULT NULL,
  `edited` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idcorso` (`idcorso`),
  KEY `createdby` (`createdby`),
  KEY `editedby` (`editedby`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Struttura stand-in per le viste `FOROthers_vw`
--
DROP VIEW IF EXISTS `FOROthers_vw`;
CREATE TABLE IF NOT EXISTS `FOROthers_vw` (
`id` mediumint(8) unsigned
,`active` tinyint(1) unsigned
,`nome` varchar(64)
,`cognome` varchar(64)
,`email` varchar(64)
,`role` enum('UNI','TCH','UDI')
,`address` varchar(255)
,`comune` varchar(255)
,`telefono` varchar(255)
,`fax` varchar(255)
,`cellulare` varchar(255)
);
-- --------------------------------------------------------

--
-- Struttura della tabella `FORProgramma`
--

DROP TABLE IF EXISTS `FORProgramma`;
CREATE TABLE IF NOT EXISTS `FORProgramma` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `idcorso` mediumint(8) unsigned NOT NULL,
  `day` int(11) NOT NULL,
  `fromh` time DEFAULT NULL,
  `toh` time DEFAULT NULL,
  `event` varchar(255) NOT NULL,
  `createdby` mediumint(8) unsigned DEFAULT NULL,
  `created` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idcorso` (`idcorso`),
  KEY `createdby` (`createdby`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Struttura della tabella `FORSede2formatori`
--

DROP TABLE IF EXISTS `FORSede2formatori`;
CREATE TABLE IF NOT EXISTS `FORSede2formatori` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `idcorso` mediumint(8) unsigned NOT NULL,
  `idsede` mediumint(8) unsigned NOT NULL,
  `idformatore` mediumint(8) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idcorso_2` (`idcorso`,`idsede`,`idformatore`),
  KEY `idcorso` (`idcorso`),
  KEY `idsede` (`idsede`),
  KEY `idformatore` (`idformatore`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Struttura della tabella `FORSede2formazione`
--

DROP TABLE IF EXISTS `FORSede2formazione`;
CREATE TABLE IF NOT EXISTS `FORSede2formazione` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `idcorso` mediumint(8) unsigned NOT NULL,
  `idsede` mediumint(8) unsigned NOT NULL,
  `idformazione` mediumint(8) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idcorso` (`idcorso`),
  KEY `idsede` (`idsede`),
  KEY `idformazione` (`idformazione`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Struttura della tabella `FORSede2uditori`
--

DROP TABLE IF EXISTS `FORSede2uditori`;
CREATE TABLE IF NOT EXISTS `FORSede2uditori` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `idcorso` mediumint(8) unsigned NOT NULL,
  `idsede` mediumint(8) unsigned NOT NULL,
  `iduditori` mediumint(8) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idcorso` (`idcorso`),
  KEY `idsede` (`idsede`),
  KEY `iduditori` (`iduditori`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Struttura della tabella `FORSedi`
--

DROP TABLE IF EXISTS `FORSedi`;
CREATE TABLE IF NOT EXISTS `FORSedi` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `sede` mediumint(8) unsigned NOT NULL,
  `corso` mediumint(8) unsigned NOT NULL,
  `giorno` datetime NOT NULL,
  `partecipanti_uff` int(11) NOT NULL,
  `partecipanti_int` int(11) NOT NULL,
  `scadenza` datetime NOT NULL,
  `stato` enum('open','closed','erased','ended') NOT NULL,
  `createdby` mediumint(8) unsigned DEFAULT NULL,
  `created` datetime NOT NULL,
  `updateby` mediumint(8) unsigned DEFAULT NULL,
  `update_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `sede` (`sede`),
  KEY `corso` (`corso`),
  KEY `createdby` (`createdby`),
  KEY `updateby` (`updateby`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Struttura della tabella `FORSedi2date`
--

DROP TABLE IF EXISTS `FORSedi2date`;
CREATE TABLE IF NOT EXISTS `FORSedi2date` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `corso` mediumint(8) unsigned NOT NULL,
  `sede` mediumint(8) unsigned NOT NULL,
  `data` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `corso` (`corso`),
  KEY `sede` (`sede`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Struttura della tabella `FORStrutture`
--

DROP TABLE IF EXISTS `FORStrutture`;
CREATE TABLE IF NOT EXISTS `FORStrutture` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `codice` varchar(255) NOT NULL,
  `sede` varchar(255) NOT NULL,
  `struttura` varchar(255) NOT NULL,
  `indirizzo` varchar(255) NOT NULL,
  `civico` varchar(15) NOT NULL,
  `localita` varchar(255) NOT NULL,
  `telefono1` varchar(255) NOT NULL,
  `telefono2` varchar(255) NOT NULL,
  `created` mediumint(8) unsigned DEFAULT NULL,
  `createdby` datetime NOT NULL,
  `editedby` mediumint(8) unsigned DEFAULT NULL,
  `edited` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `created` (`created`),
  KEY `editedby` (`editedby`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Struttura della tabella `FORUser-other`
--

DROP TABLE IF EXISTS `FORUser-other`;
CREATE TABLE IF NOT EXISTS `FORUser-other` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `iduser` mediumint(8) unsigned DEFAULT NULL,
  `role` enum('UNI','TCH','UDI') NOT NULL,
  `address` varchar(255) NOT NULL,
  `comune` varchar(255) NOT NULL,
  `telefono` varchar(255) NOT NULL,
  `fax` varchar(255) NOT NULL,
  `cellulare` varchar(255) NOT NULL,
  `nome` varchar(64) DEFAULT NULL,
  `cognome` varchar(64) DEFAULT NULL,
  `email` varchar(64) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `iduser_2` (`iduser`,`role`),
  UNIQUE KEY `iduser_3` (`iduser`,`role`),
  KEY `iduser` (`iduser`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Struttura per la vista `FOROthers_vw`
--
DROP TABLE IF EXISTS `FOROthers_vw`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `FOROthers_vw` AS select `u`.`id` AS `id`,`u`.`active` AS `active`,`m`.`nome` AS `nome`,`m`.`cognome` AS `cognome`,`m`.`email` AS `email`,`m`.`role` AS `role`,`m`.`address` AS `address`,`m`.`comune` AS `comune`,`m`.`telefono` AS `telefono`,`m`.`fax` AS `fax`,`m`.`cellulare` AS `cellulare` from (`users` `u` join `FORUser-other` `m` on((`u`.`id` = `m`.`iduser`)));

--
-- Limiti per le tabelle scaricate
--

--
-- Limiti per la tabella `FORAttestato`
--
ALTER TABLE `FORAttestato`
  ADD CONSTRAINT `fk_forattestato_corso` FOREIGN KEY (`idcorso`) REFERENCES `FORCorsi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_forattestato_createdby` FOREIGN KEY (`createdby`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_forattestato_editedby` FOREIGN KEY (`editedby`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION;

--
-- Limiti per la tabella `FORCorsi`
--
ALTER TABLE `FORCorsi`
  ADD CONSTRAINT `fk_forcorsi_createdby` FOREIGN KEY (`createdby`) REFERENCES `users` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_forcorsi_dependon` FOREIGN KEY (`dependon`) REFERENCES `FORCorsi` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_forcorsi_editedby` FOREIGN KEY (`editedby`) REFERENCES `users` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Limiti per la tabella `FORcorso2aree`
--
ALTER TABLE `FORcorso2aree`
  ADD CONSTRAINT `fk_forcorsoaree_idcorso` FOREIGN KEY (`idcorso`) REFERENCES `FORCorsi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

--
-- Limiti per la tabella `FORcorso2distict`
--
ALTER TABLE `FORcorso2distict`
  ADD CONSTRAINT `fk_forcorsodist_idcorso` FOREIGN KEY (`idcorso`) REFERENCES `FORCorsi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

--
-- Limiti per la tabella `FORcorso2files`
--
ALTER TABLE `FORcorso2files`
  ADD CONSTRAINT `fk_forcorso2files_idcorso` FOREIGN KEY (`idcorso`) REFERENCES `FORCorsi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_forcorso2files_uploadedby` FOREIGN KEY (`uploadedby`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION;

--
-- Limiti per la tabella `FORcorso2webfiles`
--
ALTER TABLE `FORcorso2webfiles`
  ADD CONSTRAINT `fk_forcorso2webfiles_idcorso` FOREIGN KEY (`idcorso`) REFERENCES `FORCorsi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_forcorso2webfiles_uploadedby` FOREIGN KEY (`uploadedby`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION;

--
-- Limiti per la tabella `FORDescrizione`
--
ALTER TABLE `FORDescrizione`
  ADD CONSTRAINT `fk_fordescrizione_createdby` FOREIGN KEY (`createdby`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_fordescrizione_editedby` FOREIGN KEY (`editedby`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_fordescrizione_idcorso` FOREIGN KEY (`idcorso`) REFERENCES `FORCorsi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

--
-- Limiti per la tabella `FORiscrizioni`
--
ALTER TABLE `FORiscrizioni`
  ADD CONSTRAINT `fk_foriscrizioni_bookedby` FOREIGN KEY (`bookedby`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_foriscrizioni_createdby` FOREIGN KEY (`createdby`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_foriscrizioni_groupamavaliduser` FOREIGN KEY (`groupamavaliduser`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_foriscrizioni_idcorso` FOREIGN KEY (`idcorso`) REFERENCES `FORCorsi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_foriscrizioni_sede` FOREIGN KEY (`sede`) REFERENCES `FORSedi` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_foriscrizioni_updateby` FOREIGN KEY (`updateby`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION,
  ADD CONSTRAINT `FORiscrizioni_ibfk_1` FOREIGN KEY (`iduser`) REFERENCES `users` (`id`);

--
-- Limiti per la tabella `formaz_corso_area`
--
ALTER TABLE `formaz_corso_area`
  ADD CONSTRAINT `formaz_corso_area_ibfk_1` FOREIGN KEY (`corso_id`) REFERENCES `formaz_corso` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Limiti per la tabella `formaz_corso_elearning`
--
ALTER TABLE `formaz_corso_elearning`
  ADD CONSTRAINT `formaz_corso_elearning_ibfk_1` FOREIGN KEY (`corso_id`) REFERENCES `formaz_corso` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

--
-- Limiti per la tabella `formaz_emails`
--
ALTER TABLE `formaz_emails`
  ADD CONSTRAINT `formaz_emails_ibfk_1` FOREIGN KEY (`corso_id`) REFERENCES `FORCorsi` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `formaz_emails_ibfk_2` FOREIGN KEY (`createdBy`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `formaz_emails_ibfk_3` FOREIGN KEY (`updatedBy`) REFERENCES `users` (`id`);

--
-- Limiti per la tabella `formaz_partecipazione`
--
ALTER TABLE `formaz_partecipazione`
  ADD CONSTRAINT `formaz_partecipazione_ibfk_1` FOREIGN KEY (`corso_id`) REFERENCES `formaz_corso` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `formaz_partecipazione_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Limiti per la tabella `formaz_partecipazione_area`
--
ALTER TABLE `formaz_partecipazione_area`
  ADD CONSTRAINT `formaz_partecipazione_area_ibfk_15` FOREIGN KEY (`partecipazione_id`) REFERENCES `formaz_partecipazione` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `formaz_partecipazione_area_ibfk_16` FOREIGN KEY (`corso_area_id`) REFERENCES `formaz_corso_area` (`id`) ON DELETE CASCADE;

--
-- Limiti per la tabella `formaz_partecipazione_elearning`
--
ALTER TABLE `formaz_partecipazione_elearning`
  ADD CONSTRAINT `formaz_partecipazione_elearning_ibfk_1` FOREIGN KEY (`partecipazione_id`) REFERENCES `formaz_partecipazione` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION,
  ADD CONSTRAINT `formaz_partecipazione_elearning_ibfk_2` FOREIGN KEY (`corso_elearning_id`) REFERENCES `formaz_corso_elearning` (`corso_id`) ON DELETE CASCADE ON UPDATE NO ACTION;

--
-- Limiti per la tabella `FORObiettivi`
--
ALTER TABLE `FORObiettivi`
  ADD CONSTRAINT `fk_forobiettivi_createdby` FOREIGN KEY (`createdby`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_forobiettivi_editedby` FOREIGN KEY (`editedby`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_forobiettivi_idcorso` FOREIGN KEY (`idcorso`) REFERENCES `FORCorsi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

--
-- Limiti per la tabella `FORProgramma`
--
ALTER TABLE `FORProgramma`
  ADD CONSTRAINT `fk_forprogramma_createdby` FOREIGN KEY (`createdby`) REFERENCES `users` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_forprogramma_idcorso` FOREIGN KEY (`idcorso`) REFERENCES `FORCorsi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

--
-- Limiti per la tabella `FORSede2formatori`
--
ALTER TABLE `FORSede2formatori`
  ADD CONSTRAINT `fk_forsedetoformatori_corso` FOREIGN KEY (`idcorso`) REFERENCES `FORCorsi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_forsedetoformatori_formatore` FOREIGN KEY (`idcorso`) REFERENCES `FORUser-other` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_forsedetoformatori_sede` FOREIGN KEY (`idsede`) REFERENCES `FORSedi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

--
-- Limiti per la tabella `FORSede2formazione`
--
ALTER TABLE `FORSede2formazione`
  ADD CONSTRAINT `fk_forsedetoformazione_corso` FOREIGN KEY (`idcorso`) REFERENCES `FORCorsi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_forsedetoformazione_formazione` FOREIGN KEY (`idformazione`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_forsedetoformazione_sede` FOREIGN KEY (`idsede`) REFERENCES `FORSedi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

--
-- Limiti per la tabella `FORSede2uditori`
--
ALTER TABLE `FORSede2uditori`
  ADD CONSTRAINT `fk_forsedetouditori_corso` FOREIGN KEY (`idcorso`) REFERENCES `FORCorsi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_forsedetouditori_sede` FOREIGN KEY (`idsede`) REFERENCES `FORSedi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_forsedetouditori_uditore` FOREIGN KEY (`idcorso`) REFERENCES `FORUser-other` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Limiti per la tabella `FORSedi`
--
ALTER TABLE `FORSedi`
  ADD CONSTRAINT `fk_forsedi_corso` FOREIGN KEY (`corso`) REFERENCES `FORCorsi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_forsedi_createdby` FOREIGN KEY (`createdby`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_forsedi_sede` FOREIGN KEY (`sede`) REFERENCES `FORStrutture` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_forsedi_updateby` FOREIGN KEY (`updateby`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION;

--
-- Limiti per la tabella `FORSedi2date`
--
ALTER TABLE `FORSedi2date`
  ADD CONSTRAINT `fk_forsedidate_corso` FOREIGN KEY (`corso`) REFERENCES `FORCorsi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_forsedidate_sede` FOREIGN KEY (`sede`) REFERENCES `FORSedi` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

--
-- Limiti per la tabella `FORStrutture`
--
ALTER TABLE `FORStrutture`
  ADD CONSTRAINT `fk_forstrutture_created` FOREIGN KEY (`created`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_forstrutture_editedby` FOREIGN KEY (`editedby`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE NO ACTION;

--
-- Limiti per la tabella `FORUser-other`
--
ALTER TABLE `FORUser-other`
  ADD CONSTRAINT `fk_foruserother_user` FOREIGN KEY (`iduser`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
