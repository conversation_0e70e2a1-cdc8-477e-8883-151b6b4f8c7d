-- ----------------------------------------------------------------------------- +
-- Date 2011/11/30
-- Author F<PERSON>
-- Reverts Patch db portaleagendo/formazione_1.0.1 -> portaleagendo/formazione_1.0 
-- ----------------------------------------------------------------------------- +

-- ----------------------------------------------------------------------------- +
-- FORCorsi
-- ----------------------------------------------------------------------------- +
ALTER TABLE  `FORCorsi` DROP FOREIGN KEY  `fk_forcorsi_createdby` ;
ALTER TABLE  `FORCorsi` DROP FOREIGN KEY  `fk_forcorsi_editedby` ;
ALTER TABLE  `FORCorsi` DROP FOREIGN KEY  `fk_forcorsi_dependon` ;
DROP INDEX `dependon` ON `FORCorsi`;
DROP INDEX `createdby` ON `FORCorsi`;
DROP INDEX `editedby` ON `FORCorsi`;
ALTER TABLE  `FORCorsi` CHANGE  `dependon`  `dependon` INT( 11 ) NOT NULL;
UPDATE `FORCorsi` SET `dependon` = 0 WHERE `dependon` IS NULL;
ALTER TABLE  `FORCorsi` CHANGE  `createdby`  `createdby` INT( 11 ) NOT NULL;
ALTER TABLE  `FORCorsi` CHANGE  `editedby`  `editedby` INT( 11 ) NOT NULL;

-- ----------------------------------------------------------------------------- +
-- FORAttestato
-- ----------------------------------------------------------------------------- +
ALTER TABLE  `FORAttestato` DROP FOREIGN KEY  `fk_forattestato_corso` ;
ALTER TABLE  `FORAttestato` DROP FOREIGN KEY  `fk_forattestato_createdby` ;
ALTER TABLE  `FORAttestato` DROP FOREIGN KEY  `fk_forattestato_editedby` ;
UPDATE `FORAttestato` SET `editedby` = 0 WHERE editedby IS NULL;
DROP INDEX `idcorso` ON `FORAttestato`;
DROP INDEX `createdby` ON `FORAttestato`;
DROP INDEX `editedby` ON `FORAttestato`;
ALTER TABLE  `FORAttestato` CHANGE  `idcorso`  `idcorso` INT( 11 ) NOT NULL;
ALTER TABLE  `FORAttestato` CHANGE  `createdby`  `createdby` INT( 11 ) NOT NULL;
ALTER TABLE  `FORAttestato` CHANGE  `editedby`  `editedby` INT( 11 ) NOT NULL;

-- ----------------------------------------------------------------------------- +
-- FORDescrizione
-- ----------------------------------------------------------------------------- +
ALTER TABLE `FORDescrizione` DROP FOREIGN KEY `fk_fordescrizione_createdby`;
ALTER TABLE `FORDescrizione` DROP FOREIGN KEY `fk_fordescrizione_editedby`;
ALTER TABLE `FORDescrizione` DROP FOREIGN KEY `fk_fordescrizione_idcorso`;
-- ripristino valori
UPDATE `FORDescrizione` SET `createdby` = 0 WHERE `createdby` IS NULL;
UPDATE `FORDescrizione` SET `editedby` = 0 WHERE `editedby` IS NULL;
DROP INDEX `createdby` ON `FORDescrizione`;
DROP INDEX `editedby` ON `FORDescrizione`;
DROP INDEX `idcorso` ON `FORDescrizione`;

ALTER TABLE `FORDescrizione` CHANGE `createdby` `createdby` INT(11) SIGNED NOT NULL;
ALTER TABLE `FORDescrizione` CHANGE `editedby` `editedby` INT(11) SIGNED NOT NULL;
ALTER TABLE `FORDescrizione` CHANGE `idcorso` `idcorso` INT(11) UNSIGNED NOT NULL;

-- Revert patch on FORObiettivi
ALTER TABLE `FORObiettivi` DROP FOREIGN KEY `fk_forobiettivi_idcorso`;
ALTER TABLE `FORObiettivi` DROP FOREIGN KEY `fk_forobiettivi_createdby`;
ALTER TABLE `FORObiettivi` DROP FOREIGN KEY `fk_forobiettivi_editedby`;

UPDATE `FORObiettivi` SET `editedby` = 0 WHERE `editedby` IS NULL;

DROP INDEX `idcorso` ON `FORObiettivi`;
DROP INDEX `createdby` ON `FORObiettivi`;
DROP INDEX `editedby` ON `FORObiettivi`;
ALTER TABLE `FORObiettivi` CHANGE `idcorso` `idcorso` INT(11) UNSIGNED NOT NULL;
ALTER TABLE `FORObiettivi` CHANGE `createdby` `createdby` INT(11) SIGNED NOT NULL;
ALTER TABLE `FORObiettivi` CHANGE `editedby` `editedby` INT(11) SIGNED NOT NULL;

-- Revert patch on FORcorso2files
ALTER TABLE `FORcorso2files` DROP FOREIGN KEY `fk_forcorso2files_idcorso`;
ALTER TABLE `FORcorso2files` DROP FOREIGN KEY `fk_forcorso2files_uploadedby`;

UPDATE `FORcorso2files` SET `uploadedby` = 0 WHERE `uploadedby` IS NULL;
DROP INDEX `idcorso` ON `FORcorso2files`;
DROP INDEX `uploadedby` ON `FORcorso2files`;
ALTER TABLE `FORcorso2files` CHANGE `idcorso` `idcorso` INT(11) SIGNED NOT NULL;
ALTER TABLE `FORcorso2files` CHANGE `uploadedby` `uploadedby` INT(11) SIGNED NOT NULL;

-- Revert patch on FORcorso2webfiles
ALTER TABLE `FORcorso2webfiles` DROP FOREIGN KEY `fk_forcorso2webfiles_idcorso`;
ALTER TABLE `FORcorso2webfiles` DROP FOREIGN KEY `fk_forcorso2webfiles_uploadedby`;

UPDATE `FORcorso2webfiles` SET `uploadedby` = 0 WHERE `uploadedby` IS NULL;
DROP INDEX `idcorso` ON `FORcorso2webfiles`;
DROP INDEX `uploadedby` ON `FORcorso2webfiles`;
ALTER TABLE `FORcorso2webfiles` CHANGE `idcorso` `idcorso` INT(11) SIGNED NOT NULL;
ALTER TABLE `FORcorso2webfiles` CHANGE `uploadedby` `uploadedby` INT(11) SIGNED NOT NULL;

-- Revert patch on FORProgramma
ALTER TABLE `FORProgramma` DROP FOREIGN KEY `fk_forprogramma_idcorso`;
ALTER TABLE `FORProgramma` DROP FOREIGN KEY `fk_forprogramma_createdby`;

UPDATE `FORProgramma` SET `createdby` = 0 WHERE `createdby` IS NULL;
DROP INDEX `idcorso` ON `FORProgramma`;
DROP INDEX `createdby` ON `FORProgramma`;
ALTER TABLE `FORProgramma` CHANGE `idcorso` `idcorso` INT(11) UNSIGNED NOT NULL;
ALTER TABLE `FORProgramma` CHANGE `createdby` `createdby` INT(11) UNSIGNED NOT NULL;



-- Revert patch on FORUser-other
ALTER TABLE `FORUser-other` DROP FOREIGN KEY `fk_foruserother_user`;
DROP INDEX `iduser` ON `FORUser-other`;
ALTER TABLE `FORUser-other` CHANGE `iduser` `iduser` INT( 11 ) SIGNED NOT NULL;

-- Revert patch on FORSede2uditori
ALTER TABLE `FORSede2uditori` DROP FOREIGN KEY `fk_forsedetouditori_corso`;
ALTER TABLE `FORSede2uditori` DROP FOREIGN KEY `fk_forsedetouditori_sede`;
ALTER TABLE `FORSede2uditori` DROP FOREIGN KEY `fk_forsedetouditori_uditore`;
DROP INDEX `idcorso` ON `FORSede2uditori`;
DROP INDEX `idsede` ON `FORSede2uditori`;
DROP INDEX `iduditori` ON `FORSede2uditori`;
ALTER TABLE  `FORSede2uditori` CHANGE  `id`  `id` INT( 11 ) SIGNED NOT NULL AUTO_INCREMENT;
ALTER TABLE  `FORSede2uditori` CHANGE  `idcorso`  `idcorso` INT( 11 ) SIGNED  NOT NULL;
ALTER TABLE  `FORSede2uditori` CHANGE  `idsede`  `idsede` INT( 11 ) SIGNED  NOT NULL;
ALTER TABLE  `FORSede2uditori` CHANGE  `iduditori`  `iduditori` INT( 11 ) SIGNED  NOT NULL;

-- Revert patch on FORSede2formazione
ALTER TABLE `FORSede2formazione` DROP FOREIGN KEY `fk_forsedetoformazione_corso`;
ALTER TABLE `FORSede2formazione` DROP FOREIGN KEY `fk_forsedetoformazione_sede`;
ALTER TABLE `FORSede2formazione` DROP FOREIGN KEY `fk_forsedetoformazione_formazione`;
DROP INDEX `idcorso` ON `FORSede2formazione`;
DROP INDEX `idsede` ON `FORSede2formazione`;
DROP INDEX `idformazione` ON `FORSede2formazione`;
ALTER TABLE  `FORSede2formazione` CHANGE  `id`  `id` INT( 11 ) SIGNED NOT NULL AUTO_INCREMENT;
ALTER TABLE  `FORSede2formazione` CHANGE  `idcorso`  `idcorso` INT( 11 ) SIGNED  NOT NULL;
ALTER TABLE  `FORSede2formazione` CHANGE  `idsede`  `idsede` INT( 11 ) SIGNED  NOT NULL;
ALTER TABLE  `FORSede2formazione` CHANGE  `idformazione`  `idformazione` INT( 11 ) SIGNED  NOT NULL;

-- Revert patch on FORSede2formatori
ALTER TABLE `FORSede2formatori` DROP FOREIGN KEY `fk_forsedetoformatori_corso`;
ALTER TABLE `FORSede2formatori` DROP FOREIGN KEY `fk_forsedetoformatori_sede`;
ALTER TABLE `FORSede2formatori` DROP FOREIGN KEY `fk_forsedetoformatori_formatore`;
DROP INDEX `idcorso` ON `FORSede2formatori`;
DROP INDEX `idsede` ON `FORSede2formatori`;
DROP INDEX `idformatore` ON `FORSede2formatori`;
ALTER TABLE  `FORSede2formatori` CHANGE  `id`  `id` INT( 11 ) SIGNED NOT NULL AUTO_INCREMENT;
ALTER TABLE  `FORSede2formatori` CHANGE  `idcorso`  `idcorso` INT( 11 ) SIGNED  NOT NULL;
ALTER TABLE  `FORSede2formatori` CHANGE  `idsede`  `idsede` INT( 11 ) SIGNED  NOT NULL;
ALTER TABLE  `FORSede2formatori` CHANGE  `idformatore`  `idformatore` INT( 11 ) SIGNED  NOT NULL;

-- Revert patch on FORiscrizioni
ALTER TABLE `FORiscrizioni` DROP FOREIGN KEY `fk_foriscrizioni_idcorso`;
ALTER TABLE `FORiscrizioni` DROP FOREIGN KEY `fk_foriscrizioni_bookedby`;
ALTER TABLE `FORiscrizioni` DROP FOREIGN KEY `fk_foriscrizioni_createdby`;
ALTER TABLE `FORiscrizioni` DROP FOREIGN KEY `fk_foriscrizioni_updateby`;
ALTER TABLE `FORiscrizioni` DROP FOREIGN KEY `fk_foriscrizioni_sede`;
ALTER TABLE `FORiscrizioni` DROP FOREIGN KEY `fk_foriscrizioni_groupamavaliduser`;
UPDATE `FORiscrizioni` SET `updateby` = 0 WHERE `updateby` IS NULL;
UPDATE `FORiscrizioni` SET `bookedby` = 0 WHERE `bookedby` IS NULL;
UPDATE `FORiscrizioni` SET `createdby` = 0 WHERE `createdby` IS NULL;
UPDATE `FORiscrizioni` SET `sede` = 0 WHERE `sede` IS NULL;
UPDATE `FORiscrizioni` SET `groupamavaliduser` = 0 WHERE `groupamavaliduser` IS NULL;
DROP INDEX `idcorso` ON `FORiscrizioni`;
DROP INDEX `bookedby` ON `FORiscrizioni`;
DROP INDEX `sede` ON `FORiscrizioni`;
DROP INDEX `createdby` ON `FORiscrizioni`;
DROP INDEX `updateby` ON `FORiscrizioni`;
DROP INDEX `groupamavaliduser` ON `FORiscrizioni`;
ALTER TABLE `FORiscrizioni` CHANGE `updateby` `updateby` INT(11) SIGNED NOT NULL;
ALTER TABLE `FORiscrizioni` CHANGE `createdby` `createdby` INT(11) SIGNED NOT NULL;
ALTER TABLE `FORiscrizioni` CHANGE  `sede`  `sede` INT( 11 ) SIGNED NOT NULL;
ALTER TABLE `FORiscrizioni` CHANGE `bookedby` `bookedby` INT( 11 ) SIGNED NULL;
ALTER TABLE `FORiscrizioni` CHANGE  `idcorso`  `idcorso` INT( 11 ) SIGNED NOT NULL;
ALTER TABLE `FORiscrizioni` CHANGE `groupamavaliduser` `groupamavaliduser` INT(11) SIGNED NOT NULL;

-- Revert patch on FORSedi
ALTER TABLE `FORSedi` DROP FOREIGN KEY `fk_forsedi_sede`;
ALTER TABLE `FORSedi` DROP FOREIGN KEY `fk_forsedi_corso`;
ALTER TABLE `FORSedi` DROP FOREIGN KEY `fk_forsedi_createdby`;
ALTER TABLE `FORSedi` DROP FOREIGN KEY `fk_forsedi_updateby`;
UPDATE `FORSedi` SET `updateby` = 0 WHERE `updateby` IS NULL;
DROP INDEX `sede` ON `FORSedi`;
DROP INDEX `corso` ON `FORSedi`;
DROP INDEX `createdby` ON `FORSedi`;
DROP INDEX `updateby` ON `FORSedi`;
ALTER TABLE  `FORSedi` CHANGE  `id`  `id` INT( 11 ) SIGNED NOT NULL AUTO_INCREMENT;
ALTER TABLE `FORSedi` CHANGE `sede` `sede` INT(11) SIGNED NOT NULL;
ALTER TABLE  `FORSedi` CHANGE  `corso`  `corso` INT( 11 ) SIGNED NOT NULL;
ALTER TABLE `FORSedi` CHANGE `createdby` `createdby` INT(11) SIGNED NOT NULL;
ALTER TABLE `FORSedi` CHANGE `updateby` `updateby` INT(11) SIGNED NOT NULL;

-- Revert patch on FORStrutture
ALTER TABLE `FORStrutture` DROP FOREIGN KEY `fk_forstrutture_created`;
ALTER TABLE `FORStrutture` DROP FOREIGN KEY `fk_forstrutture_editedby`;
UPDATE `FORStrutture` SET `editedby` = 0 WHERE `editedby` IS NULL;
DROP INDEX `created` ON `FORStrutture`;
DROP INDEX `editedby` ON `FORStrutture`;
ALTER TABLE `FORStrutture` CHANGE `created` `created` INT(11) SIGNED NOT NULL;
ALTER TABLE `FORStrutture` CHANGE `editedby` `editedby` INT(11) SIGNED NOT NULL;
ALTER TABLE `FORStrutture` CHANGE  `id`  `id` INT( 11 ) SIGNED NOT NULL AUTO_INCREMENT;

-- Revert FORcorso2aree
ALTER TABLE `FORcorso2aree` DROP FOREIGN KEY `fk_forcorsoaree_idcorso`;
DROP INDEX `idcorso` ON `FORcorso2aree`;
ALTER TABLE  `FORcorso2aree` CHANGE  `idcorso`  `idcorso` INT( 11 ) SIGNED NOT NULL;

-- Revert FORcorso2distict
ALTER TABLE `FORcorso2distict` DROP FOREIGN KEY `fk_forcorsodist_idcorso`;
DROP INDEX `idcorso` ON `FORcorso2distict`;
ALTER TABLE  `FORcorso2distict` CHANGE  `idcorso`  `idcorso` INT( 11 ) SIGNED NOT NULL;

-- Revert FORSedi2date
ALTER TABLE  `FORSedi2date` DROP FOREIGN KEY `fk_forsedidate_corso`;
ALTER TABLE  `FORSedi2date` DROP FOREIGN KEY `fk_forsedidate_sede`;
DROP INDEX `corso` ON `FORSedi2date`;
DROP INDEX `sede` ON `FORSedi2date`;
ALTER TABLE  `FORSedi2date` CHANGE  `id`  `id` INT( 11 ) SIGNED NOT NULL;
ALTER TABLE  `FORSedi2date` CHANGE  `corso`  `corso` INT( 11 ) SIGNED NOT NULL;
ALTER TABLE  `FORSedi2date` CHANGE  `sede`  `sede` INT( 11 ) SIGNED NOT NULL;


