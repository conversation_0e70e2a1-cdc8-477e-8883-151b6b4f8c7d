-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version *******
-- http://www.phpmyadmin.net
--
-- Host: localhost
-- Generato il: Dic 03, 2012 alle 17:30
-- Versione del server: 5.1.63
-- Versione PHP: 5.3.2-1ubuntu4.17

SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;

--
-- Database: `portaleagendo`
--

-- --------------------------------------------------------

--
-- <PERSON><PERSON><PERSON><PERSON> della tabella `formaz_corso_elearning`
--

DROP TABLE IF EXISTS `formaz_corso_elearning`;
CREATE TABLE IF NOT EXISTS `formaz_corso_elearning` (
  `corso_id` mediumint(8) unsigned NOT NULL,
  `idRemoto` varchar(31) NOT NULL,
  PRIMARY KEY (`corso_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Struttura della tabella `formaz_elearning_fetch_session`
--

DROP TABLE IF EXISTS `formaz_elearning_fetch_session`;
CREATE TABLE IF NOT EXISTS `formaz_elearning_fetch_session` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `date` datetime NOT NULL,
  `dateInterval0` datetime NOT NULL,
  `dateInterval1` datetime NOT NULL,
  `count` mediumint(9) NOT NULL,
  `success` tinyint(1) NOT NULL,
  `message` varchar(127) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Struttura della tabella `formaz_partecipazione_elearning`
--

DROP TABLE IF EXISTS `formaz_partecipazione_elearning`;
CREATE TABLE IF NOT EXISTS `formaz_partecipazione_elearning` (
  `partecipazione_id` mediumint(8) unsigned NOT NULL,
  `corso_elearning_id` mediumint(8) unsigned NOT NULL,
  `punteggio` tinyint(1) unsigned NOT NULL,
  `dataCompletamento` datetime NOT NULL,
  PRIMARY KEY (`partecipazione_id`),
  KEY `corso_elearning_id` (`corso_elearning_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Limiti per le tabelle scaricate
--

--
-- Limiti per la tabella `formaz_corso_elearning`
--
ALTER TABLE `formaz_corso_elearning`
  ADD CONSTRAINT `formaz_corso_elearning_ibfk_1` FOREIGN KEY (`corso_id`) REFERENCES `formaz_corso` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION;

--
-- Limiti per la tabella `formaz_partecipazione_elearning`
--
ALTER TABLE `formaz_partecipazione_elearning`
  ADD CONSTRAINT `formaz_partecipazione_elearning_ibfk_1` FOREIGN KEY (`partecipazione_id`) REFERENCES `formaz_partecipazione` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION,
  ADD CONSTRAINT `formaz_partecipazione_elearning_ibfk_2` FOREIGN KEY (`corso_elearning_id`) REFERENCES `formaz_corso_elearning` (`corso_id`) ON DELETE CASCADE ON UPDATE NO ACTION;
SET FOREIGN_KEY_CHECKS=1;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
